import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
from enum import Enum
import collections

class MarketState(Enum):
    STRONG_BULLISH = "强烈看涨"
    MODERATE_BULLISH = "温和看涨"
    NEUTRAL = "中性"
    MODERATE_BEARISH = "温和看跌"
    STRONG_BEARISH = "强烈看跌"

class TradingSignal(Enum):
    STRONG_BUY = "强烈买入"
    BUY = "买入"
    HOLD = "持有"
    SELL = "卖出"
    STRONG_SELL = "强烈卖出"
    NO_TRADE = "不交易"

@dataclass
class MarketData:
    prices: List[float]
    volumes: List[float] = None
    timestamps: List[str] = None

@dataclass
class TradingResult:
    y_value: float
    market_state: MarketState
    signal: TradingSignal
    kelly_position: float
    recommended_position: float
    expected_return: float
    confidence: float

class QuantitativeTradingSystem:
    def __init__(self, base_win_rate=0.67, base_odds_ratio=2.0, risk_factor=0.5):
        self.base_win_rate = base_win_rate
        self.base_odds_ratio = base_odds_ratio
        self.risk_factor = risk_factor

    def calculate_linear_regression(self, prices: List[float]) -> Tuple[float, float, float]:
        n = len(prices)
        if n < 2:
            return 0, 0, 0

        x = np.array(range(1, n + 1))
        y = np.array(prices)

        x_avg = np.mean(x)
        y_avg = np.mean(y)

        sum_xy = np.sum((x - x_avg) * (y - y_avg))
        sum_xx = np.sum((x - x_avg) ** 2)

        if sum_xx == 0:
            return 0, 0, 0

        slope = sum_xy / sum_xx
        intercept = y_avg - slope * x_avg

        y_pred = intercept + slope * x
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - y_avg) ** 2)

        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

        return slope, intercept, r_squared

    def estimate_y_value(self, slope: float, r_squared: float) -> Tuple[float, MarketState]:
        if slope > 1.0:
            base_y = 0.75
            state = MarketState.STRONG_BULLISH
        elif slope > 0.5:
            base_y = 0.65
            state = MarketState.MODERATE_BULLISH
        elif slope > -0.5:
            base_y = 0.5
            state = MarketState.NEUTRAL
        elif slope > -1.0:
            base_y = 0.35
            state = MarketState.MODERATE_BEARISH
        else:
            base_y = 0.25
            state = MarketState.STRONG_BEARISH

        confidence_factor = min(1.0, r_squared)
        adjusted_y = 0.5 + (base_y - 0.5) * confidence_factor

        return adjusted_y, state

    def generate_trading_signal(self, y_value: float, market_state: MarketState,
                              r_squared: float) -> TradingSignal:
        if r_squared < 0.3:
            return TradingSignal.NO_TRADE

        if y_value > 0.4:
            if y_value > 0.6:
                return TradingSignal.STRONG_BUY
            else:
                return TradingSignal.BUY
        elif y_value < 0.33:
            if y_value < 0.25:
                return TradingSignal.STRONG_SELL
            else:
                return TradingSignal.SELL
        else:
            return TradingSignal.NO_TRADE

    def calculate_kelly_position(self, y_value: float, signal: TradingSignal) -> Tuple[float, float]:
        if signal == TradingSignal.NO_TRADE:
            return 0, 0

        if y_value > 0.6 or y_value < 0.25:
            adjusted_win_rate = min(0.8, self.base_win_rate + 0.1)
        elif y_value > 0.4 or y_value < 0.33:
            adjusted_win_rate = min(0.75, self.base_win_rate + 0.05)
        else:
            adjusted_win_rate = self.base_win_rate

        kelly_fraction = (self.base_odds_ratio * adjusted_win_rate - (1 - adjusted_win_rate)) / self.base_odds_ratio
        kelly_fraction = max(0, kelly_fraction)
        
        safe_position = kelly_fraction * self.risk_factor

        if signal in [TradingSignal.STRONG_BUY, TradingSignal.STRONG_SELL]:
            recommended_position = safe_position
        else:
            recommended_position = safe_position * 0.7

        return kelly_fraction, min(recommended_position, 0.3)

    def analyze_stock(self, market_data: MarketData) -> TradingResult:
        slope, intercept, r_squared = self.calculate_linear_regression(market_data.prices)
        y_value, market_state = self.estimate_y_value(slope, r_squared)
        signal = self.generate_trading_signal(y_value, market_state, r_squared)
        kelly_position, recommended_position = self.calculate_kelly_position(y_value, signal)

        if signal != TradingSignal.NO_TRADE:
            win_rate = min(0.8, self.base_win_rate + (0.1 if abs(y_value - 0.5) > 0.1 else 0))
            expected_return = win_rate * self.base_odds_ratio - (1 - win_rate)
        else:
            expected_return = 0

        return TradingResult(
            y_value=y_value,
            market_state=market_state,
            signal=signal,
            kelly_position=kelly_position,
            recommended_position=recommended_position,
            expected_return=expected_return,
            confidence=r_squared
        )

    def backtest_strategy(self, historical_data: Dict[str, List[float]],
                         initial_capital: float = 100000) -> Dict:
        results = []
        total_capital = initial_capital

        for stock_name, prices in historical_data.items():
            if len(prices) < 20:
                continue

            market_data = MarketData(prices=prices)
            analysis_result = self.analyze_stock(market_data)

            if analysis_result.signal != TradingSignal.NO_TRADE:
                position_size = analysis_result.recommended_position
                trade_amount = total_capital * position_size

                if analysis_result.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                    profit = trade_amount * analysis_result.expected_return * 0.1
                else:
                    profit = trade_amount * analysis_result.expected_return * 0.05

                total_capital += profit

            results.append({
                'stock': stock_name,
                'y_value': analysis_result.y_value,
                'signal': analysis_result.signal.value,
                'position': analysis_result.recommended_position,
                'expected_return': analysis_result.expected_return,
                'confidence': analysis_result.confidence
            })

        return {
            'final_capital': total_capital,
            'total_return': (total_capital - initial_capital) / initial_capital,
            'trades': results
        }

def load_csv_data(file_path):
    df = pd.read_csv(file_path)
    return df['Close'].values

def main():
    # Load CSV data
    file_path = 'HK0001.csv'
    prices = load_csv_data(file_path)

    # Create market data object
    market_data = MarketData(prices=prices)

    # Initialize trading system
    trading_system = QuantitativeTradingSystem(
        base_win_rate=0.67,
        base_odds_ratio=2.0,
        risk_factor=0.5
    )

    # Analyze the stock
    result = trading_system.analyze_stock(market_data)

    print("Stock Analysis Results:")
    print("-" * 40)
    print(f"Y Value: {result.y_value:.3f}")
    print(f"Market State: {result.market_state.value}")
    print(f"Trading Signal: {result.signal.value}")
    print(f"Recommended Position: {result.recommended_position:.1%}")
    print(f"Expected Return: {result.expected_return:.2f}")
    print(f"Confidence: {result.confidence:.3f}")
    print()

    # Run backtest
    backtest_result = trading_system.backtest_strategy({'HK0001': prices}, 100000)

    print("Backtest Results:")
    print("-" * 40)
    print(f"Initial Capital: ¥{100000:,}")
    print(f"Final Capital: ¥{backtest_result['final_capital']:,.0f}")
    print(f"Total Return: {backtest_result['total_return']:.1%}")

    window_size = 20
    results = []
    for i in range(len(prices) - window_size + 1):
        window_prices = prices[i:i+window_size]
        market_data = MarketData(prices=window_prices)
        result = trading_system.analyze_stock(market_data)
        results.append({
            'start_index': i,
            'end_index': i + window_size - 1,
            'y_value': result.y_value,
            'signal': result.signal.value,
            'recommended_position': result.recommended_position,
            'expected_return': result.expected_return,
            'confidence': result.confidence
        })

    # 输出全部窗口结果
    print("\n滑动窗口分析结果（全部窗口）：")
    print("start\tend\tY值\t信号\t推荐仓位\t期望收益\t置信度")
    for r in results:
        print(f"{r['start_index']}\t{r['end_index']}\t{r['y_value']:.3f}\t{r['signal']}\t{r['recommended_position']:.1%}\t{r['expected_return']:.2f}\t{r['confidence']:.3f}")

    # 统计分析表
    signals = [r['signal'] for r in results]
    y_values = [float(r['y_value']) for r in results]
    confidences = [float(r['confidence']) for r in results]
    positions = [float(r['recommended_position']) for r in results]

    signal_counts = collections.Counter(signals)
    total = len(signals)
    print("\n统计分析表：")
    print("信号\t出现次数\t占比")
    for sig, cnt in signal_counts.items():
        print(f"{sig}\t{cnt}\t{cnt/total:.1%}")

    def stat_line(arr, name, percent=False):
        arr = np.array(arr)
        if percent:
            arr = arr * 100
        print(f"{name}: 平均={np.mean(arr):.2f}{'%' if percent else ''}\t中位={np.median(arr):.2f}{'%' if percent else ''}\t最大={np.max(arr):.2f}{'%' if percent else ''}\t最小={np.min(arr):.2f}{'%' if percent else ''}\tStd={np.std(arr):.2f}{'%' if percent else ''}")

    print("\nY值统计:")
    stat_line(y_values, "Y值")
    print("置信度统计:")
    stat_line(confidences, "置信度")
    print("推荐仓位统计:")
    stat_line(positions, "推荐仓位", percent=True)

    # 智能结论
    print("\n【智能分析结论】")
    main_signal = max(signal_counts, key=signal_counts.get)
    print(f"主要交易信号: {main_signal} ({signal_counts[main_signal]/total:.1%})")
    if signal_counts.get('买入',0) + signal_counts.get('强烈买入',0) > signal_counts.get('卖出',0) + signal_counts.get('强烈卖出',0):
        print("市场整体偏多，建议关注多头机会。")
    elif signal_counts.get('卖出',0) + signal_counts.get('强烈卖出',0) > signal_counts.get('买入',0) + signal_counts.get('强烈买入',0):
        print("市场整体偏空，建议关注空头机会。")
    else:
        print("市场多空均衡，建议谨慎操作。")
    print(f"平均推荐仓位为{np.mean(positions)*100:.1f}%，平均置信度为{np.mean(confidences):.3f}。")
    if np.mean(confidences) < 0.3:
        print("整体置信度较低，建议减少交易频率或观望。")
    elif np.mean(confidences) > 0.7:
        print("整体置信度较高，可适当加大仓位。")
    else:
        print("置信度中等，建议适度参与。")

if __name__ == "__main__":
    main()