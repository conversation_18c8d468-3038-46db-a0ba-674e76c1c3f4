#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析如何使股民在y∈[0.3333333, 0.4]范围内无论怎样买卖都赚不到钱
"""

def calculate_expected_value(a, b, c, d, x, y):
    """计算期望值E"""
    return (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d

def analyze_zero_profit_range():
    """
    分析使股民期望收益≤0的条件
    E = (a-b-c+d)xy + (b-d)x + (c-d)y + d
    """
    
    print("="*80)
    print("分析如何使股民在y∈[0.3333333, 0.4]范围内无法盈利")
    print("="*80)
    
    # 目标y范围
    y_min, y_max = 1/3, 0.4
    
    print(f"目标y范围：[{y_min:.6f}, {y_max:.6f}]")
    print("期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print()
    
    # 理论分析
    print("【理论分析】")
    print("-" * 60)
    print("设 A = a-b-c+d, B = b-d, C = c-d, D = d")
    print("则 E = Axy + Bx + Cy + D = x(Ay + B) + Cy + D")
    print()
    print("要使E ≤ 0对所有x ∈ [0,1]都成立，需要：")
    print("1. 当Ay + B ≥ 0时，E在x=1处最大，需要 (Ay + B) + Cy + D ≤ 0")
    print("2. 当Ay + B ≤ 0时，E在x=0处最大，需要 Cy + D ≤ 0")
    print()
    
    # 寻找合适的参数组合
    print("【寻找合适的参数组合】")
    print("-" * 60)
    
    # 尝试不同的参数组合
    test_cases = []
    
    # 生成一些测试案例
    for a in [1, 5, 10, 20]:
        for d in [1, 5, 10, 20]:
            for b in [-20, -10, -5, -1]:
                for c in [-20, -10, -5, -1]:
                    # 检查这个组合是否在目标y范围内能使E≤0
                    A = a - b - c + d
                    B = b - d
                    C = c - d
                    D = d
                    
                    # 检查在y_min和y_max处的最大E值
                    max_E_at_y_min = max(
                        (A * y_min + B) + C * y_min + D if A * y_min + B >= 0 else C * y_min + D,
                        C * y_min + D
                    )
                    
                    max_E_at_y_max = max(
                        (A * y_max + B) + C * y_max + D if A * y_max + B >= 0 else C * y_max + D,
                        C * y_max + D
                    )
                    
                    # 如果在整个范围内最大E都≤0，则这是一个有效组合
                    if max_E_at_y_min <= 0.01 and max_E_at_y_max <= 0.01:  # 允许小误差
                        test_cases.append((a, b, c, d, max_E_at_y_min, max_E_at_y_max))
    
    if test_cases:
        print(f"找到 {len(test_cases)} 个使股民无法盈利的参数组合：")
        print("a\tb\tc\td\t最大E(y=1/3)\t最大E(y=0.4)")
        print("-" * 60)
        for i, (a, b, c, d, e_min, e_max) in enumerate(test_cases[:10]):  # 只显示前10个
            print(f"{a}\t{b}\t{c}\t{d}\t{e_min:.6f}\t{e_max:.6f}")
    else:
        print("在给定约束下未找到使股民完全无法盈利的参数组合")
    
    print()
    
    # 详细分析一个具体例子
    print("【具体例子分析】")
    print("-" * 60)
    
    # 尝试构造一个例子
    # 我们希望在y=1/3时，E的最大值为0
    # 设a=3, d=3, b=-6, c=-6
    a, b, c, d = 3, -6, -6, 3
    
    print(f"例子：a={a}, b={b}, c={c}, d={d}")
    
    A = a - b - c + d  # 3-(-6)-(-6)+3 = 18
    B = b - d          # -6-3 = -9
    C = c - d          # -6-3 = -9
    D = d              # 3
    
    print(f"A = {A}, B = {B}, C = {C}, D = {D}")
    print(f"E = {A}xy + {B}x + {C}y + {D}")
    print(f"E = x({A}y + {B}) + {C}y + {D}")
    print()
    
    # 分析在目标y范围内的情况
    for y in [y_min, y_max]:
        coeff_x = A * y + B  # x的系数
        const_term = C * y + D  # 常数项
        
        print(f"当y = {y:.6f}时：")
        print(f"  E = x({coeff_x:.6f}) + {const_term:.6f}")
        
        if coeff_x >= 0:
            max_E = coeff_x + const_term  # x=1时最大
            optimal_x = 1.0
        else:
            max_E = const_term  # x=0时最大
            optimal_x = 0.0
        
        print(f"  最大E = {max_E:.6f} (当x = {optimal_x}时)")
        print(f"  股民{'能' if max_E > 0 else '不能'}盈利")
        print()
    
    # 验证计算
    print("【验证计算】")
    print("-" * 60)
    x_test_values = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    
    for y in [y_min, y_max]:
        print(f"当y = {y:.6f}时，不同x值的E：")
        for x in x_test_values:
            E = calculate_expected_value(a, b, c, d, x, y)
            print(f"  x = {x:.1f}, E = {E:.6f}")
        print()

def find_theoretical_solution():
    """寻找理论解"""
    print("【理论解法】")
    print("-" * 60)
    
    y_min, y_max = 1/3, 0.4
    
    print("要使股民在y∈[1/3, 0.4]范围内无法盈利，需要：")
    print("对于任意y∈[1/3, 0.4]和任意x∈[0,1]，都有E ≤ 0")
    print()
    
    print("关键约束条件：")
    print("1. 当y = 1/3时，max E ≤ 0")
    print("2. 当y = 0.4时，max E ≤ 0")
    print()
    
    print("由于E = x(Ay + B) + Cy + D，其中：")
    print("A = a-b-c+d, B = b-d, C = c-d, D = d")
    print()
    
    print("在y = 1/3时：")
    print("- 如果A/3 + B ≥ 0，则max E = A/3 + B + C/3 + D")
    print("- 如果A/3 + B < 0，则max E = C/3 + D")
    print()
    
    print("在y = 0.4时：")
    print("- 如果0.4A + B ≥ 0，则max E = 0.4A + B + 0.4C + D")
    print("- 如果0.4A + B < 0，则max E = 0.4C + D")
    print()
    
    print("简化分析：")
    print("如果我们设置参数使得在整个范围内都有Ay + B < 0，")
    print("那么只需要确保 Cy + D ≤ 0 在y∈[1/3, 0.4]内成立。")
    print()
    
    print("这要求：")
    print("C/3 + D ≤ 0  且  0.4C + D ≤ 0")
    print("即：(c-d)/3 + d ≤ 0  且  0.4(c-d) + d ≤ 0")
    print("即：c/3 - d/3 + d ≤ 0  且  0.4c - 0.4d + d ≤ 0")
    print("即：c/3 + 2d/3 ≤ 0  且  0.4c + 0.6d ≤ 0")
    print()
    
    print("如果c < 0, d > 0，这些条件很难同时满足。")
    print("这解释了为什么在常规参数范围内很难找到完全零收益的解。")

def main():
    analyze_zero_profit_range()
    find_theoretical_solution()

if __name__ == "__main__":
    main()
