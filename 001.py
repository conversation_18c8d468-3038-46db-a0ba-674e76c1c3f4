# 使用示例
trading_system = QuantitativeTradingSystem(
    base_win_rate=0.67,    # 你的67%胜率
    base_odds_ratio=2.0,   # 你的1:2赔率
    risk_factor=0.5        # 半凯利策略
)

# 分析单只股票
market_data = MarketData(prices=[100, 102, 105, 108, 112, 115])
result = trading_system.analyze_stock(market_data)

# 获得完整的交易建议
print(f"Y值: {result.y_value}")
print(f"交易信号: {result.signal.value}")
print(f"推荐仓位: {result.recommended_position:.1%}")