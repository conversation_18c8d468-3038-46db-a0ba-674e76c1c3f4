#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析MySQL存储过程实现的线性回归方法
对应用户的 sp_averagelineV3 存储过程
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def simulate_mysql_regression(prices, debug=False):
    """
    模拟MySQL存储过程的线性回归计算
    
    参数:
    prices: 收市价格列表
    debug: 是否输出调试信息
    """
    
    # 步骤1：创建索引列 i (从1开始)
    n = len(prices)
    i_values = list(range(1, n + 1))
    
    # 步骤2：计算平均值
    Xavg = n / 2  # 这里有个问题：应该是 (n+1)/2
    Yavg = np.mean(prices)
    
    if debug:
        print(f"总行数: {n}")
        print(f"Xavg (时间平均): {Xavg}")
        print(f"Yavg (价格平均): {Yavg:.6f}")
    
    # 步骤3：计算回归系数
    sum_xy = sum((i - Xavg) * (price - Yavg) for i, price in zip(i_values, prices))
    sum_xx = sum((i - Xavg) ** 2 for i in i_values)
    
    # 计算斜率和截距
    b = sum_xy / sum_xx  # 斜率
    m = Yavg - b * Xavg  # 截距
    
    if debug:
        print(f"sum_xy: {sum_xy:.6f}")
        print(f"sum_xx: {sum_xx:.6f}")
        print(f"斜率 b: {b:.6f}")
        print(f"截距 m: {m:.6f}")
    
    # 步骤4：计算拟合值
    fitted_values = [m + b * i for i in i_values]
    
    return {
        'intercept': m,
        'slope': b,
        'sum_xy': sum_xy,
        'sum_xx': sum_xx,
        'Xavg': Xavg,
        'Yavg': Yavg,
        'total_rows': n,
        'fitted_values': fitted_values,
        'i_values': i_values,
        'prices': prices
    }

def analyze_mysql_method():
    """分析MySQL方法的特点"""
    
    print("="*80)
    print("🗄️  MySQL存储过程线性回归分析")
    print("="*80)
    print()
    
    print("【存储过程分析】")
    print("-" * 60)
    print("你的存储过程实现了以下功能：")
    print("1. ✅ 自动添加索引列 'i'")
    print("2. ✅ 计算时间序列的线性回归")
    print("3. ✅ 生成拟合的'中值'列")
    print("4. ✅ 返回所有回归参数")
    print()
    
    print("回归公式：中值 = m + b * i")
    print("其中：")
    print("  i: 时间索引（1, 2, 3, ...）")
    print("  收市: 股票收盘价")
    print("  中值: 拟合的趋势线值")
    print()
    
    # 模拟一个股票价格序列
    print("【模拟测试】")
    print("-" * 60)
    
    # 生成模拟数据：有上升趋势 + 噪音
    np.random.seed(42)
    n_days = 30
    base_price = 100
    trend = 0.5  # 每天上涨0.5元
    noise_level = 2
    
    true_prices = [base_price + trend * i + np.random.normal(0, noise_level) 
                   for i in range(n_days)]
    
    # 使用模拟的MySQL方法
    result = simulate_mysql_regression(true_prices, debug=True)
    
    print()
    print("回归结果解释：")
    print(f"• 截距 m = {result['intercept']:.6f} (第0天的预测价格)")
    print(f"• 斜率 b = {result['slope']:.6f} (每天的平均涨跌幅)")
    print(f"• 如果b > 0: 上升趋势")
    print(f"• 如果b < 0: 下降趋势")
    print()

def connect_to_y_estimation():
    """连接到Y值估计的应用"""
    
    print("【与Y值估计的连接】")
    print("-" * 60)
    print("你的MySQL方法计算的是股价的时间趋势，但要估计控股商托价概率Y，")
    print("还需要进一步的分析：")
    print()
    
    print("方法1：基于趋势强度估计Y")
    print("• 强上升趋势 (b >> 0) → 可能Y值较高 (控股商托价)")
    print("• 强下降趋势 (b << 0) → 可能Y值较低 (控股商压价)")
    print("• 横盘整理 (b ≈ 0) → Y值可能在中间区间")
    print()
    
    print("方法2：结合价格偏离度")
    print("• 实际价格 > 拟合价格 → 可能有托价行为")
    print("• 实际价格 < 拟合价格 → 可能有压价行为")
    print("• 偏离度可以作为Y值估计的参考")
    print()
    
    # 演示如何从趋势估计Y值
    print("【Y值估计示例】")
    print("-" * 60)
    
    # 模拟不同市场情况
    scenarios = [
        ("强烈上涨", [100, 102, 105, 108, 112, 115, 119, 123, 127, 132]),
        ("温和上涨", [100, 101, 102, 103, 104, 105, 106, 107, 108, 109]),
        ("横盘整理", [100, 99, 101, 100, 102, 99, 101, 100, 102, 101]),
        ("温和下跌", [100, 99, 98, 97, 96, 95, 94, 93, 92, 91]),
        ("强烈下跌", [100, 97, 94, 90, 86, 82, 78, 74, 70, 66])
    ]
    
    print("市场情况\t\t斜率b\t\t估计Y值\t解释")
    print("-" * 70)
    
    for scenario_name, prices in scenarios:
        result = simulate_mysql_regression(prices)
        slope = result['slope']
        
        # 简单的Y值估计逻辑
        if slope > 1.0:
            estimated_y = 0.7  # 强上涨，高托价概率
        elif slope > 0.3:
            estimated_y = 0.6  # 温和上涨
        elif slope > -0.3:
            estimated_y = 0.5  # 横盘
        elif slope > -1.0:
            estimated_y = 0.4  # 温和下跌
        else:
            estimated_y = 0.3  # 强下跌，低托价概率
        
        interpretation = ""
        if estimated_y > 0.6:
            interpretation = "可能有托价行为"
        elif estimated_y < 0.4:
            interpretation = "可能有压价行为"
        else:
            interpretation = "中性，观望"
        
        print(f"{scenario_name}\t\t{slope:6.3f}\t\t{estimated_y:.1f}\t{interpretation}")
    
    print()

def practical_implementation_guide():
    """实际实施指南"""
    
    print("【实际实施指南】")
    print("-" * 60)
    
    print("步骤1：数据准备")
    print("• 确保有足够的历史数据（建议至少20-30个交易日）")
    print("• 数据质量要好，避免异常值")
    print("• 可以使用日线、周线或其他时间周期")
    print()
    
    print("步骤2：运行回归分析")
    print("• 调用存储过程：CALL sp_averagelineV3('your_table_name')")
    print("• 获取斜率b和截距m")
    print("• 检查拟合质量（可以计算R²）")
    print()
    
    print("步骤3：Y值估计")
    print("• 基于斜率b的大小和方向")
    print("• 结合价格偏离度")
    print("• 考虑成交量等其他指标")
    print()
    
    print("步骤4：应用选择性策略")
    print("• 如果估计Y > 0.4 或 Y < 0.33，考虑交易")
    print("• 如果0.33 ≤ Y ≤ 0.4，避免交易")
    print("• 结合其他技术分析确认")
    print()
    
    print("改进建议：")
    print("🎯 添加R²计算来评估拟合质量")
    print("🎯 考虑使用滑动窗口更新回归")
    print("🎯 结合成交量分析")
    print("🎯 添加异常值检测和处理")
    print()

def suggest_enhancements():
    """建议的改进"""
    
    print("【建议的存储过程改进】")
    print("-" * 60)
    
    print("1. 添加R²计算：")
    print("""
    -- 计算R²
    SET @ss_res = 0;
    SET @ss_tot = 0;
    SET stmt_sql = CONCAT('SELECT SUM(POWER(收市 - (', m, ' + ', b, ' * i), 2)) INTO @ss_res FROM `', tablename, '`');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET stmt_sql = CONCAT('SELECT SUM(POWER(收市 - ', Yavg, ', 2)) INTO @ss_tot FROM `', tablename, '`');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET r_squared = 1 - (@ss_res / @ss_tot);
    """)
    
    print("2. 添加偏离度计算：")
    print("""
    -- 添加偏离度列
    ALTER TABLE tablename ADD COLUMN 偏离度 DECIMAL(20, 6);
    UPDATE tablename SET 偏离度 = (收市 - 中值) / 中值 * 100;
    """)
    
    print("3. 添加Y值估计：")
    print("""
    -- 基于斜率估计Y值
    SET estimated_y = CASE 
        WHEN b > 1.0 THEN 0.7
        WHEN b > 0.3 THEN 0.6
        WHEN b > -0.3 THEN 0.5
        WHEN b > -1.0 THEN 0.4
        ELSE 0.3
    END;
    """)

def main():
    analyze_mysql_method()
    connect_to_y_estimation()
    practical_implementation_guide()
    suggest_enhancements()
    
    print("\n" + "="*80)
    print("🎯 总结：MySQL线性回归方法评价")
    print("="*80)
    print()
    print("【方法评价：✅ 实用的技术实现】")
    print("-" * 60)
    print()
    print("优点：")
    print("• 📊 数据库层面实现，处理大数据高效")
    print("• 🔄 可以批量处理多只股票")
    print("• 💾 结果直接存储在数据库中")
    print("• 🎯 自动化程度高")
    print()
    print("与Y值估计的结合：")
    print("• ✅ 可以基于趋势斜率估计Y值")
    print("• ✅ 结合价格偏离度提高准确性")
    print("• ✅ 适合你的选择性交易策略")
    print()
    print("建议：")
    print("• 🎯 添加R²计算评估拟合质量")
    print("• 🎯 结合成交量等其他指标")
    print("• 🎯 使用滑动窗口保持时效性")
    print("• 🎯 建立Y值估计的量化规则")

if __name__ == "__main__":
    main()
