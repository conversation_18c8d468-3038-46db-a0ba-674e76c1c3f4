#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凯利公式在股票投资中的应用分析
结合马丁格尔、网格等策略
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

def kelly_formula(win_rate, odds_ratio):
    """
    凯利公式计算最优仓位比例
    
    参数:
    win_rate: 胜率 (0-1之间)
    odds_ratio: 赔率比 (盈利/亏损的比例)
    
    公式: f* = (bp - q) / b
    其中: b = 赔率比, p = 胜率, q = 败率 = 1-p
    """
    if win_rate <= 0 or win_rate >= 1:
        return 0
    
    lose_rate = 1 - win_rate
    kelly_fraction = (odds_ratio * win_rate - lose_rate) / odds_ratio
    
    return max(0, kelly_fraction)  # 确保不为负数

def analyze_kelly_parameters():
    """分析用户提到的凯利公式参数"""
    
    print("="*80)
    print("📊 凯利公式分析：赔率1:2，胜率67%")
    print("="*80)
    print()
    
    # 用户参数
    odds_ratio = 2.0  # 1:2赔率，即盈利是亏损的2倍
    win_rate = 0.67   # 67%胜率
    
    print("【参数分析】")
    print("-" * 60)
    print(f"赔率比 (b): {odds_ratio} (盈利是亏损的{odds_ratio}倍)")
    print(f"胜率 (p): {win_rate*100}%")
    print(f"败率 (q): {(1-win_rate)*100}%")
    print()
    
    # 计算凯利比例
    kelly_fraction = kelly_formula(win_rate, odds_ratio)
    
    print("【凯利公式计算】")
    print("-" * 60)
    print("凯利公式: f* = (bp - q) / b")
    print(f"f* = ({odds_ratio} × {win_rate} - {1-win_rate}) / {odds_ratio}")
    print(f"f* = ({odds_ratio * win_rate:.3f} - {1-win_rate:.3f}) / {odds_ratio}")
    print(f"f* = {odds_ratio * win_rate - (1-win_rate):.3f} / {odds_ratio}")
    print(f"f* = {kelly_fraction:.4f}")
    print()
    print(f"✅ 最优仓位比例: {kelly_fraction*100:.2f}%")
    print()
    
    # 期望收益计算
    expected_return = win_rate * odds_ratio - (1 - win_rate)
    print(f"期望收益率: {win_rate} × {odds_ratio} - {1-win_rate} = {expected_return:.4f}")
    print(f"即每次交易期望收益: {expected_return*100:.2f}%")
    print()
    
    return kelly_fraction, expected_return

def simulate_kelly_strategy():
    """模拟凯利策略的长期表现"""
    
    print("【凯利策略模拟】")
    print("-" * 60)
    
    # 参数设置
    odds_ratio = 2.0
    win_rate = 0.67
    kelly_fraction = kelly_formula(win_rate, odds_ratio)
    
    # 模拟1000次交易
    np.random.seed(42)
    num_trades = 1000
    initial_capital = 100000  # 10万初始资金
    
    # 不同仓位策略的对比
    strategies = {
        '凯利公式': kelly_fraction,
        '半凯利': kelly_fraction * 0.5,
        '四分之一凯利': kelly_fraction * 0.25,
        '固定10%': 0.1,
        '固定20%': 0.2
    }
    
    results = {}
    
    for strategy_name, position_size in strategies.items():
        capital = initial_capital
        capital_history = [capital]
        
        for _ in range(num_trades):
            # 根据胜率决定交易结果
            if np.random.random() < win_rate:
                # 胜利：获得position_size * odds_ratio的收益
                capital *= (1 + position_size * odds_ratio)
            else:
                # 失败：损失position_size
                capital *= (1 - position_size)
            
            capital_history.append(capital)
        
        results[strategy_name] = {
            'final_capital': capital,
            'total_return': (capital - initial_capital) / initial_capital,
            'history': capital_history
        }
    
    # 输出结果
    print("策略对比 (1000次交易后):")
    print("策略名称\t\t最终资金\t\t总收益率\t年化收益率*")
    print("-" * 70)
    
    for strategy_name, result in results.items():
        final_capital = result['final_capital']
        total_return = result['total_return']
        # 假设1000次交易相当于4年（每年250次交易）
        annualized_return = (final_capital / initial_capital) ** (1/4) - 1
        
        print(f"{strategy_name:<15}\t{final_capital:>12,.0f}\t{total_return:>8.1%}\t{annualized_return:>8.1%}")
    
    print("\n*假设1000次交易 = 4年")
    print()
    
    return results

def analyze_martingale_grid_strategies():
    """分析马丁格尔和网格策略"""
    
    print("【马丁格尔和网格策略分析】")
    print("-" * 60)
    
    print("1. 马丁格尔策略 (Martingale):")
    print("   • 原理：每次亏损后加倍下注")
    print("   • 优点：理论上能保证盈利")
    print("   • 风险：需要无限资金，实际中风险极高")
    print("   • 与凯利结合：用凯利公式控制初始仓位")
    print()
    
    print("2. 网格策略 (Grid Trading):")
    print("   • 原理：在价格区间内设置买卖网格")
    print("   • 优点：适合震荡市场")
    print("   • 风险：单边趋势市场容易爆仓")
    print("   • 与凯利结合：用凯利公式分配每个网格的资金")
    print()
    
    print("3. 结合凯利公式的改进:")
    print("   • 用Y值判断市场状态")
    print("   • 在有利区间(Y>40%或Y<33%)使用凯利仓位")
    print("   • 在不利区间(33%≤Y≤40%)降低仓位或停止交易")
    print("   • 动态调整策略参数")
    print()

def simulate_combined_strategy():
    """模拟结合Y值判断的凯利策略"""
    
    print("【结合Y值的凯利策略模拟】")
    print("-" * 60)
    
    # 模拟市场环境
    np.random.seed(42)
    num_periods = 200
    
    # 生成Y值序列（模拟控股商策略变化）
    y_values = []
    for i in range(num_periods):
        # Y值有周期性变化
        base_y = 0.5 + 0.2 * np.sin(i * 0.1) + np.random.normal(0, 0.05)
        y_values.append(np.clip(base_y, 0.1, 0.9))
    
    # 策略参数
    odds_ratio = 2.0
    base_win_rate = 0.67
    kelly_fraction = kelly_formula(base_win_rate, odds_ratio)
    
    initial_capital = 100000
    capital = initial_capital
    capital_history = [capital]
    
    trades_taken = 0
    trades_skipped = 0
    
    for y in y_values:
        # 根据Y值决定是否交易
        if y > 0.4 or y < 0.33:
            # 在有利区间交易
            trades_taken += 1
            
            # 根据Y值调整胜率
            if y > 0.6:
                adjusted_win_rate = min(0.8, base_win_rate + 0.1)  # Y值高时胜率提升
            elif y < 0.3:
                adjusted_win_rate = min(0.8, base_win_rate + 0.05)  # Y值低时胜率略提升
            else:
                adjusted_win_rate = base_win_rate
            
            # 重新计算凯利比例
            adjusted_kelly = kelly_formula(adjusted_win_rate, odds_ratio)
            position_size = adjusted_kelly * 0.5  # 使用半凯利降低风险
            
            # 模拟交易结果
            if np.random.random() < adjusted_win_rate:
                capital *= (1 + position_size * odds_ratio)
            else:
                capital *= (1 - position_size)
        else:
            # 在不利区间跳过交易
            trades_skipped += 1
        
        capital_history.append(capital)
    
    total_return = (capital - initial_capital) / initial_capital
    
    print(f"模拟结果 ({num_periods}个周期):")
    print(f"初始资金: {initial_capital:,}")
    print(f"最终资金: {capital:,.0f}")
    print(f"总收益率: {total_return:.1%}")
    print(f"交易次数: {trades_taken}")
    print(f"跳过次数: {trades_skipped}")
    print(f"交易频率: {trades_taken/(trades_taken+trades_skipped):.1%}")
    print()

def practical_implementation_guide():
    """实际实施指南"""
    
    print("【实际实施指南】")
    print("-" * 60)
    
    print("步骤1：确定基础参数")
    print("• 通过历史回测确定真实的胜率和赔率")
    print("• 用你的Y值估计方法判断市场状态")
    print("• 计算基础凯利比例")
    print()
    
    print("步骤2：风险控制")
    print("• 使用半凯利或四分之一凯利降低风险")
    print("• 设置最大仓位限制（如不超过30%）")
    print("• 设置止损点")
    print()
    
    print("步骤3：动态调整")
    print("• 根据Y值调整交易频率")
    print("• Y > 40% 或 Y < 33%：正常交易")
    print("• 33% ≤ Y ≤ 40%：降低仓位或停止交易")
    print()
    
    print("步骤4：策略组合")
    print("• 可以结合网格策略在震荡市使用")
    print("• 避免在单边市场使用马丁格尔")
    print("• 定期评估和调整参数")
    print()
    
    print("关键提醒：")
    print("⚠️  凯利公式假设参数稳定，实际中需要动态调整")
    print("⚠️  过度使用凯利公式可能导致高波动")
    print("⚠️  始终保持风险意识，不要过度杠杆")

def main():
    kelly_fraction, expected_return = analyze_kelly_parameters()
    results = simulate_kelly_strategy()
    analyze_martingale_grid_strategies()
    simulate_combined_strategy()
    practical_implementation_guide()
    
    print("\n" + "="*80)
    print("🎯 总结：凯利公式在你的策略中的应用")
    print("="*80)
    print()
    print("【核心优势】")
    print("-" * 60)
    print("• ✅ 数学基础扎实：凯利公式提供最优仓位")
    print("• ✅ 风险可控：通过Y值判断避开不利时机")
    print("• ✅ 收益优化：在有利时机使用合理仓位")
    print("• ✅ 策略完整：从选股到仓位管理的完整体系")
    print()
    print("【实际应用建议】")
    print("-" * 60)
    print(f"• 🎯 基础凯利比例：{kelly_fraction*100:.1f}%")
    print("• 🎯 建议使用半凯利降低风险")
    print("• 🎯 结合Y值判断优化交易时机")
    print("• 🎯 定期回测调整参数")
    print()
    print("你的策略组合非常专业：")
    print("线性回归估计Y值 → 选择性交易 → 凯利公式仓位管理")
    print("这是一个完整的量化交易系统！")

if __name__ == "__main__":
    main()
