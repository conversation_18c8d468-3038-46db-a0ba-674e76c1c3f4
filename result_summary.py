#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票博弈论分析结果总结
"""

def calculate_expected_value(a, b, c, d, x, y):
    """计算期望值E"""
    E = (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d
    return E

def main():
    print("="*80)
    print("股票博弈论分析结果总结")
    print("="*80)
    
    # 固定参数
    y = 0.495858373
    
    print(f"固定参数：y (控股商托价概率) = {y}")
    print(f"期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print()
    
    # 最优解
    print("【最优解】")
    print("-" * 40)
    a_opt, b_opt, c_opt, d_opt, x_opt = 50, -1, -1, 50, 0.1
    E_opt = calculate_expected_value(a_opt, b_opt, c_opt, d_opt, x_opt, y)
    
    print(f"a (股民买升，控股商托价收益): {a_opt}")
    print(f"b (股民买跌，控股商托价收益): {b_opt}")
    print(f"c (股民买升，控股商压价收益): {c_opt}")
    print(f"d (股民买跌，控股商压价收益): {d_opt}")
    print(f"x (股民买升概率): {x_opt}")
    print(f"y (控股商托价概率): {y}")
    print(f"最大期望收益 E: {E_opt:.6f}")
    print()
    
    # 详细计算过程
    print("【计算验证】")
    print("-" * 40)
    term1 = (a_opt - b_opt - c_opt + d_opt) * x_opt * y
    term2 = (b_opt - d_opt) * x_opt
    term3 = (c_opt - d_opt) * y
    term4 = d_opt
    
    print(f"E = ({a_opt}-({b_opt})-({c_opt})+{d_opt})*{x_opt}*{y} + ({b_opt}-{d_opt})*{x_opt} + ({c_opt}-{d_opt})*{y} + {d_opt}")
    print(f"  = {a_opt - b_opt - c_opt + d_opt}*{x_opt}*{y} + {b_opt - d_opt}*{x_opt} + {c_opt - d_opt}*{y} + {d_opt}")
    print(f"  = {term1:.6f} + {term2:.6f} + {term3:.6f} + {term4:.6f}")
    print(f"  = {E_opt:.6f}")
    print()
    
    # 分析不同x值的影响
    print("【不同x值对E的影响】(使用最优a,b,c,d参数)")
    print("-" * 40)
    x_values = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    for x in x_values:
        E = calculate_expected_value(a_opt, b_opt, c_opt, d_opt, x, y)
        print(f"x = {x:.1f}, E = {E:.6f}")
    print()
    
    # 其他优秀组合
    print("【其他优秀参数组合】")
    print("-" * 40)
    combinations = [
        (50, -1, -1, 50, 0.2),
        (49, -1, -1, 50, 0.1),
        (50, -2, -1, 50, 0.1),
        (48, -1, -1, 50, 0.1),
        (50, -1, -1, 50, 0.3)
    ]
    
    for i, (a, b, c, d, x) in enumerate(combinations, 1):
        E = calculate_expected_value(a, b, c, d, x, y)
        print(f"{i}. a={a}, b={b}, c={c}, d={d}, x={x}, E={E:.6f}")
    print()
    
    # 关键发现
    print("【关键发现】")
    print("-" * 40)
    print("1. 最优策略特征：")
    print("   - a和d应该取最大值(50)，以最大化正收益")
    print("   - b和c应该取最小绝对值(-1)，以最小化负收益")
    print("   - x应该取较小值(0.1)，即股民应该以较低概率买升")
    print()
    print("2. 经济学解释：")
    print("   - 当控股商托价概率约为0.496时，股民最优策略是以10%概率买升")
    print("   - 在最极端的收益差异下(a=50, b=-1, c=-1, d=50)，期望收益最大")
    print("   - 这表明在给定的控股商策略下，股民应该采取保守的买跌策略")
    print()
    print("3. 博弈论含义：")
    print("   - 控股商的托价概率接近50%，接近随机策略")
    print("   - 股民的最优反应是偏向买跌(90%概率)")
    print("   - 最大期望收益约为24.67元")

if __name__ == "__main__":
    main()
