#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的量化交易算法
整合：Y值估计 + 选择性交易 + 凯利公式 + 马丁/网格策略
基于博弈论的智能交易系统
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
from enum import Enum

class MarketState(Enum):
    """市场状态枚举"""
    STRONG_BULLISH = "强烈看涨"
    MODERATE_BULLISH = "温和看涨"
    NEUTRAL = "中性"
    MODERATE_BEARISH = "温和看跌"
    STRONG_BEARISH = "强烈看跌"

class TradingSignal(Enum):
    """交易信号枚举"""
    STRONG_BUY = "强烈买入"
    BUY = "买入"
    HOLD = "持有"
    SELL = "卖出"
    STRONG_SELL = "强烈卖出"
    NO_TRADE = "不交易"

@dataclass
class MarketData:
    """市场数据结构"""
    prices: List[float]
    volumes: List[float] = None
    timestamps: List[str] = None

@dataclass
class TradingResult:
    """交易结果结构"""
    y_value: float
    market_state: MarketState
    signal: TradingSignal
    kelly_position: float
    recommended_position: float
    expected_return: float
    confidence: float

class QuantitativeTradingSystem:
    """完整的量化交易系统"""

    def __init__(self, base_win_rate=0.67, base_odds_ratio=2.0, risk_factor=0.5):
        self.base_win_rate = base_win_rate
        self.base_odds_ratio = base_odds_ratio
        self.risk_factor = risk_factor  # 风险控制因子，用于调整凯利比例

    def calculate_linear_regression(self, prices: List[float]) -> Tuple[float, float, float]:
        """
        计算线性回归，模拟MySQL存储过程的功能
        返回：(斜率, 截距, R²)
        """
        n = len(prices)
        if n < 2:
            return 0, 0, 0

        # 创建时间索引
        x = np.array(range(1, n + 1))
        y = np.array(prices)

        # 计算平均值
        x_avg = np.mean(x)
        y_avg = np.mean(y)

        # 计算回归系数
        sum_xy = np.sum((x - x_avg) * (y - y_avg))
        sum_xx = np.sum((x - x_avg) ** 2)

        if sum_xx == 0:
            return 0, 0, 0

        slope = sum_xy / sum_xx
        intercept = y_avg - slope * x_avg

        # 计算R²
        y_pred = intercept + slope * x
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - y_avg) ** 2)

        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

        return slope, intercept, r_squared

    def estimate_y_value(self, slope: float, r_squared: float) -> Tuple[float, MarketState]:
        """
        基于趋势斜率估计控股商托价概率Y值
        """
        # 基于斜率的基础估计
        if slope > 1.0:
            base_y = 0.75
            state = MarketState.STRONG_BULLISH
        elif slope > 0.5:
            base_y = 0.65
            state = MarketState.MODERATE_BULLISH
        elif slope > -0.5:
            base_y = 0.5
            state = MarketState.NEUTRAL
        elif slope > -1.0:
            base_y = 0.35
            state = MarketState.MODERATE_BEARISH
        else:
            base_y = 0.25
            state = MarketState.STRONG_BEARISH

        # 根据R²调整置信度
        confidence_factor = min(1.0, r_squared)
        adjusted_y = 0.5 + (base_y - 0.5) * confidence_factor

        return adjusted_y, state

    def generate_trading_signal(self, y_value: float, market_state: MarketState,
                              r_squared: float) -> TradingSignal:
        """
        基于Y值和市场状态生成交易信号
        核心策略：只在Y > 40% 或 Y < 33% 时交易
        """
        # 置信度检查
        if r_squared < 0.3:
            return TradingSignal.NO_TRADE

        # 选择性交易策略
        if y_value > 0.4:
            # 控股商可能托价，适合买入
            if y_value > 0.6:
                return TradingSignal.STRONG_BUY
            else:
                return TradingSignal.BUY
        elif y_value < 0.33:
            # 控股商可能压价，适合卖出或做空
            if y_value < 0.25:
                return TradingSignal.STRONG_SELL
            else:
                return TradingSignal.SELL
        else:
            # 危险区间，避免交易
            return TradingSignal.NO_TRADE

    def calculate_kelly_position(self, y_value: float, signal: TradingSignal) -> Tuple[float, float]:
        """
        计算凯利公式推荐的仓位
        返回：(理论凯利比例, 实际推荐比例)
        """
        if signal == TradingSignal.NO_TRADE:
            return 0, 0

        # 根据Y值调整胜率
        if y_value > 0.6 or y_value < 0.25:
            # 极端情况，胜率提升
            adjusted_win_rate = min(0.8, self.base_win_rate + 0.1)
        elif y_value > 0.4 or y_value < 0.33:
            # 有利情况，胜率略提升
            adjusted_win_rate = min(0.75, self.base_win_rate + 0.05)
        else:
            adjusted_win_rate = self.base_win_rate

        # 计算凯利比例
        kelly_fraction = kelly_formula(adjusted_win_rate, self.base_odds_ratio)

        # 应用风险控制
        safe_position = kelly_fraction * self.risk_factor

        # 根据信号强度进一步调整
        if signal in [TradingSignal.STRONG_BUY, TradingSignal.STRONG_SELL]:
            recommended_position = safe_position
        else:
            recommended_position = safe_position * 0.7  # 更保守

        return kelly_fraction, min(recommended_position, 0.3)  # 最大仓位限制30%

    def analyze_stock(self, market_data: MarketData) -> TradingResult:
        """
        完整的股票分析流程
        """
        # 1. 线性回归分析
        slope, intercept, r_squared = self.calculate_linear_regression(market_data.prices)

        # 2. 估计Y值
        y_value, market_state = self.estimate_y_value(slope, r_squared)

        # 3. 生成交易信号
        signal = self.generate_trading_signal(y_value, market_state, r_squared)

        # 4. 计算仓位
        kelly_position, recommended_position = self.calculate_kelly_position(y_value, signal)

        # 5. 计算期望收益
        if signal != TradingSignal.NO_TRADE:
            win_rate = min(0.8, self.base_win_rate + (0.1 if abs(y_value - 0.5) > 0.1 else 0))
            expected_return = win_rate * self.base_odds_ratio - (1 - win_rate)
        else:
            expected_return = 0

        return TradingResult(
            y_value=y_value,
            market_state=market_state,
            signal=signal,
            kelly_position=kelly_position,
            recommended_position=recommended_position,
            expected_return=expected_return,
            confidence=r_squared
        )

    def backtest_strategy(self, historical_data: Dict[str, List[float]],
                         initial_capital: float = 100000) -> Dict:
        """
        策略回测
        """
        results = []
        total_capital = initial_capital

        for stock_name, prices in historical_data.items():
            if len(prices) < 20:  # 需要足够的历史数据
                continue

            market_data = MarketData(prices=prices)
            analysis_result = self.analyze_stock(market_data)

            # 模拟交易
            if analysis_result.signal != TradingSignal.NO_TRADE:
                position_size = analysis_result.recommended_position
                trade_amount = total_capital * position_size

                # 简化的收益计算
                if analysis_result.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                    # 假设按期望收益率计算
                    profit = trade_amount * analysis_result.expected_return * 0.1  # 保守估计
                else:
                    profit = trade_amount * analysis_result.expected_return * 0.05  # 做空收益更保守

                total_capital += profit

            results.append({
                'stock': stock_name,
                'y_value': analysis_result.y_value,
                'signal': analysis_result.signal.value,
                'position': analysis_result.recommended_position,
                'expected_return': analysis_result.expected_return,
                'confidence': analysis_result.confidence
            })

        return {
            'final_capital': total_capital,
            'total_return': (total_capital - initial_capital) / initial_capital,
            'trades': results
        }

def kelly_formula(win_rate, odds_ratio):
    """
    凯利公式计算最优仓位比例
    
    参数:
    win_rate: 胜率 (0-1之间)
    odds_ratio: 赔率比 (盈利/亏损的比例)
    
    公式: f* = (bp - q) / b
    其中: b = 赔率比, p = 胜率, q = 败率 = 1-p
    """
    if win_rate <= 0 or win_rate >= 1:
        return 0
    
    lose_rate = 1 - win_rate
    kelly_fraction = (odds_ratio * win_rate - lose_rate) / odds_ratio
    
    return max(0, kelly_fraction)  # 确保不为负数

def analyze_kelly_parameters():
    """分析用户提到的凯利公式参数"""
    
    print("="*80)
    print("📊 凯利公式分析：赔率1:2，胜率67%")
    print("="*80)
    print()
    
    # 用户参数
    odds_ratio = 2.0  # 1:2赔率，即盈利是亏损的2倍
    win_rate = 0.67   # 67%胜率
    
    print("【参数分析】")
    print("-" * 60)
    print(f"赔率比 (b): {odds_ratio} (盈利是亏损的{odds_ratio}倍)")
    print(f"胜率 (p): {win_rate*100}%")
    print(f"败率 (q): {(1-win_rate)*100}%")
    print()
    
    # 计算凯利比例
    kelly_fraction = kelly_formula(win_rate, odds_ratio)
    
    print("【凯利公式计算】")
    print("-" * 60)
    print("凯利公式: f* = (bp - q) / b")
    print(f"f* = ({odds_ratio} × {win_rate} - {1-win_rate}) / {odds_ratio}")
    print(f"f* = ({odds_ratio * win_rate:.3f} - {1-win_rate:.3f}) / {odds_ratio}")
    print(f"f* = {odds_ratio * win_rate - (1-win_rate):.3f} / {odds_ratio}")
    print(f"f* = {kelly_fraction:.4f}")
    print()
    print(f"✅ 最优仓位比例: {kelly_fraction*100:.2f}%")
    print()
    
    # 期望收益计算
    expected_return = win_rate * odds_ratio - (1 - win_rate)
    print(f"期望收益率: {win_rate} × {odds_ratio} - {1-win_rate} = {expected_return:.4f}")
    print(f"即每次交易期望收益: {expected_return*100:.2f}%")
    print()
    
    return kelly_fraction, expected_return

def simulate_kelly_strategy():
    """模拟凯利策略的长期表现"""
    
    print("【凯利策略模拟】")
    print("-" * 60)
    
    # 参数设置
    odds_ratio = 2.0
    win_rate = 0.67
    kelly_fraction = kelly_formula(win_rate, odds_ratio)
    
    # 模拟1000次交易
    np.random.seed(42)
    num_trades = 1000
    initial_capital = 100000  # 10万初始资金
    
    # 不同仓位策略的对比
    strategies = {
        '凯利公式': kelly_fraction,
        '半凯利': kelly_fraction * 0.5,
        '四分之一凯利': kelly_fraction * 0.25,
        '固定10%': 0.1,
        '固定20%': 0.2
    }
    
    results = {}
    
    for strategy_name, position_size in strategies.items():
        capital = initial_capital
        capital_history = [capital]
        
        for _ in range(num_trades):
            # 根据胜率决定交易结果
            if np.random.random() < win_rate:
                # 胜利：获得position_size * odds_ratio的收益
                capital *= (1 + position_size * odds_ratio)
            else:
                # 失败：损失position_size
                capital *= (1 - position_size)
            
            capital_history.append(capital)
        
        results[strategy_name] = {
            'final_capital': capital,
            'total_return': (capital - initial_capital) / initial_capital,
            'history': capital_history
        }
    
    # 输出结果
    print("策略对比 (1000次交易后):")
    print("策略名称\t\t最终资金\t\t总收益率\t年化收益率*")
    print("-" * 70)
    
    for strategy_name, result in results.items():
        final_capital = result['final_capital']
        total_return = result['total_return']
        # 假设1000次交易相当于4年（每年250次交易）
        annualized_return = (final_capital / initial_capital) ** (1/4) - 1
        
        print(f"{strategy_name:<15}\t{final_capital:>12,.0f}\t{total_return:>8.1%}\t{annualized_return:>8.1%}")
    
    print("\n*假设1000次交易 = 4年")
    print()
    
    return results

def analyze_martingale_grid_strategies():
    """分析马丁格尔和网格策略"""
    
    print("【马丁格尔和网格策略分析】")
    print("-" * 60)
    
    print("1. 马丁格尔策略 (Martingale):")
    print("   • 原理：每次亏损后加倍下注")
    print("   • 优点：理论上能保证盈利")
    print("   • 风险：需要无限资金，实际中风险极高")
    print("   • 与凯利结合：用凯利公式控制初始仓位")
    print()
    
    print("2. 网格策略 (Grid Trading):")
    print("   • 原理：在价格区间内设置买卖网格")
    print("   • 优点：适合震荡市场")
    print("   • 风险：单边趋势市场容易爆仓")
    print("   • 与凯利结合：用凯利公式分配每个网格的资金")
    print()
    
    print("3. 结合凯利公式的改进:")
    print("   • 用Y值判断市场状态")
    print("   • 在有利区间(Y>40%或Y<33%)使用凯利仓位")
    print("   • 在不利区间(33%≤Y≤40%)降低仓位或停止交易")
    print("   • 动态调整策略参数")
    print()

def simulate_combined_strategy():
    """模拟结合Y值判断的凯利策略"""
    
    print("【结合Y值的凯利策略模拟】")
    print("-" * 60)
    
    # 模拟市场环境
    np.random.seed(42)
    num_periods = 200
    
    # 生成Y值序列（模拟控股商策略变化）
    y_values = []
    for i in range(num_periods):
        # Y值有周期性变化
        base_y = 0.5 + 0.2 * np.sin(i * 0.1) + np.random.normal(0, 0.05)
        y_values.append(np.clip(base_y, 0.1, 0.9))
    
    # 策略参数
    odds_ratio = 2.0
    base_win_rate = 0.67
    kelly_fraction = kelly_formula(base_win_rate, odds_ratio)
    
    initial_capital = 100000
    capital = initial_capital
    capital_history = [capital]
    
    trades_taken = 0
    trades_skipped = 0
    
    for y in y_values:
        # 根据Y值决定是否交易
        if y > 0.4 or y < 0.33:
            # 在有利区间交易
            trades_taken += 1
            
            # 根据Y值调整胜率
            if y > 0.6:
                adjusted_win_rate = min(0.8, base_win_rate + 0.1)  # Y值高时胜率提升
            elif y < 0.3:
                adjusted_win_rate = min(0.8, base_win_rate + 0.05)  # Y值低时胜率略提升
            else:
                adjusted_win_rate = base_win_rate
            
            # 重新计算凯利比例
            adjusted_kelly = kelly_formula(adjusted_win_rate, odds_ratio)
            position_size = adjusted_kelly * 0.5  # 使用半凯利降低风险
            
            # 模拟交易结果
            if np.random.random() < adjusted_win_rate:
                capital *= (1 + position_size * odds_ratio)
            else:
                capital *= (1 - position_size)
        else:
            # 在不利区间跳过交易
            trades_skipped += 1
        
        capital_history.append(capital)
    
    total_return = (capital - initial_capital) / initial_capital
    
    print(f"模拟结果 ({num_periods}个周期):")
    print(f"初始资金: {initial_capital:,}")
    print(f"最终资金: {capital:,.0f}")
    print(f"总收益率: {total_return:.1%}")
    print(f"交易次数: {trades_taken}")
    print(f"跳过次数: {trades_skipped}")
    print(f"交易频率: {trades_taken/(trades_taken+trades_skipped):.1%}")
    print()

def practical_implementation_guide():
    """实际实施指南"""
    
    print("【实际实施指南】")
    print("-" * 60)
    
    print("步骤1：确定基础参数")
    print("• 通过历史回测确定真实的胜率和赔率")
    print("• 用你的Y值估计方法判断市场状态")
    print("• 计算基础凯利比例")
    print()
    
    print("步骤2：风险控制")
    print("• 使用半凯利或四分之一凯利降低风险")
    print("• 设置最大仓位限制（如不超过30%）")
    print("• 设置止损点")
    print()
    
    print("步骤3：动态调整")
    print("• 根据Y值调整交易频率")
    print("• Y > 40% 或 Y < 33%：正常交易")
    print("• 33% ≤ Y ≤ 40%：降低仓位或停止交易")
    print()
    
    print("步骤4：策略组合")
    print("• 可以结合网格策略在震荡市使用")
    print("• 避免在单边市场使用马丁格尔")
    print("• 定期评估和调整参数")
    print()
    
    print("关键提醒：")
    print("⚠️  凯利公式假设参数稳定，实际中需要动态调整")
    print("⚠️  过度使用凯利公式可能导致高波动")
    print("⚠️  始终保持风险意识，不要过度杠杆")

def demonstrate_complete_algorithm():
    """演示完整的量化交易算法"""

    print("="*80)
    print("🚀 完整量化交易算法演示")
    print("="*80)
    print()

    # 初始化交易系统
    trading_system = QuantitativeTradingSystem(
        base_win_rate=0.67,
        base_odds_ratio=2.0,
        risk_factor=0.5  # 使用半凯利
    )

    print("【系统参数】")
    print("-" * 60)
    print(f"基础胜率: {trading_system.base_win_rate*100}%")
    print(f"基础赔率: 1:{trading_system.base_odds_ratio}")
    print(f"风险因子: {trading_system.risk_factor} (半凯利策略)")
    print()

    # 模拟不同类型的股票数据
    stock_scenarios = {
        "强势上涨股": [100, 102, 105, 108, 112, 115, 119, 123, 127, 132, 136, 140, 145, 150, 155],
        "温和上涨股": [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114],
        "横盘整理股": [100, 99, 101, 100, 102, 99, 101, 100, 102, 101, 100, 99, 101, 100, 102],
        "温和下跌股": [100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86],
        "急速下跌股": [100, 97, 94, 90, 86, 82, 78, 74, 70, 66, 62, 58, 54, 50, 46]
    }

    print("【个股分析结果】")
    print("-" * 60)
    print("股票类型\t\tY值\t趋势\t\t交易信号\t推荐仓位\t期望收益\t置信度")
    print("-" * 90)

    all_results = []
    for stock_name, prices in stock_scenarios.items():
        market_data = MarketData(prices=prices)
        result = trading_system.analyze_stock(market_data)

        all_results.append((stock_name, result))

        print(f"{stock_name:<12}\t{result.y_value:.3f}\t{result.market_state.value:<8}\t"
              f"{result.signal.value:<8}\t{result.recommended_position:.1%}\t\t"
              f"{result.expected_return:.2f}\t\t{result.confidence:.3f}")

    print()

    # 策略统计
    tradeable_stocks = [r for _, r in all_results if r.signal != TradingSignal.NO_TRADE]

    print("【策略统计】")
    print("-" * 60)
    print(f"总分析股票数: {len(all_results)}")
    print(f"可交易股票数: {len(tradeable_stocks)}")
    print(f"交易选择率: {len(tradeable_stocks)/len(all_results):.1%}")

    if tradeable_stocks:
        avg_position = np.mean([r.recommended_position for r in tradeable_stocks])
        avg_expected_return = np.mean([r.expected_return for r in tradeable_stocks])
        avg_confidence = np.mean([r.confidence for r in tradeable_stocks])

        print(f"平均推荐仓位: {avg_position:.1%}")
        print(f"平均期望收益: {avg_expected_return:.2f}")
        print(f"平均置信度: {avg_confidence:.3f}")

    print()

    # 回测演示
    print("【回测演示】")
    print("-" * 60)
    backtest_result = trading_system.backtest_strategy(stock_scenarios, 100000)

    print(f"初始资金: ¥{100000:,}")
    print(f"最终资金: ¥{backtest_result['final_capital']:,.0f}")
    print(f"总收益率: {backtest_result['total_return']:.1%}")
    print()

    

def main():
    # 运行完整算法演示
    demonstrate_complete_algorithm()

   
if __name__ == "__main__":
    main()
