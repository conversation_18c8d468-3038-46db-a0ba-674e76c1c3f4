#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从赔率角度分析股票博弈论问题
如果a、b、c、d是赔率而不是绝对收益，分析股民是否能赚到钱
"""

import numpy as np
import matplotlib.pyplot as plt

def calculate_expected_return_odds(a, b, c, d, x, y, bet_amount=1):
    """
    从赔率角度计算期望收益
    
    参数:
    a, b, c, d: 四种情况的赔率
    x: 股民买升的概率
    y: 控股商托价的概率
    bet_amount: 投注金额
    
    赔率解释：
    - 正赔率表示每投注1元可以净赚多少元
    - 负赔率表示需要投注多少元才能净赚1元
    """
    
    # 四种情况的概率和对应的净收益
    # 情况1：股民买升，控股商托价 (概率: x*y)
    if a > 0:
        net_gain_1 = a * bet_amount  # 净赚a倍投注额
    else:
        net_gain_1 = bet_amount / abs(a)  # 需要投注|a|倍才能净赚1元，所以净赚1/|a|倍
    
    # 情况2：股民买跌，控股商托价 (概率: (1-x)*y)
    if b > 0:
        net_gain_2 = b * bet_amount
    else:
        net_gain_2 = bet_amount / abs(b)
    
    # 情况3：股民买升，控股商压价 (概率: x*(1-y))
    if c > 0:
        net_gain_3 = c * bet_amount
    else:
        net_gain_3 = bet_amount / abs(c)
    
    # 情况4：股民买跌，控股商压价 (概率: (1-x)*(1-y))
    if d > 0:
        net_gain_4 = d * bet_amount
    else:
        net_gain_4 = bet_amount / abs(d)
    
    # 计算期望收益
    expected_return = (x * y * net_gain_1 + 
                      (1-x) * y * net_gain_2 + 
                      x * (1-y) * net_gain_3 + 
                      (1-x) * (1-y) * net_gain_4)
    
    return expected_return

def analyze_fair_odds():
    """分析公平赔率的情况"""
    
    print("="*80)
    print("📊 从赔率角度分析股票博弈")
    print("="*80)
    print()
    
    print("【赔率概念说明】")
    print("-" * 60)
    print("在博彩/投资中，赔率表示投注回报：")
    print("• 正赔率(如+2.0)：投注1元，赢了净赚2元")
    print("• 负赔率(如-2.0)：需要投注2元，赢了净赚1元")
    print("• 赔率反映了事件发生的概率和庄家的利润")
    print()
    
    print("【公平赔率理论】")
    print("-" * 60)
    print("如果赔率是公平的（无庄家优势），那么：")
    print("期望收益 = 0")
    print()
    print("这意味着长期来看，玩家既不赚钱也不亏钱")
    print("所有的收益都被概率平衡掉了")
    print()
    
    # 分析具体例子
    print("【具体例子分析】")
    print("-" * 60)
    
    # 例子1：对称赔率
    print("例子1：对称赔率情况")
    a, b, c, d = 1.0, -1.0, -1.0, 1.0  # 对称的赔率设置
    y = 0.5  # 控股商50%概率托价
    
    print(f"赔率设置：a={a}, b={b}, c={c}, d={d}")
    print(f"控股商托价概率：y={y}")
    print()
    
    x_values = np.linspace(0, 1, 11)
    print("不同买升概率x的期望收益：")
    for x in x_values:
        expected = calculate_expected_return_odds(a, b, c, d, x, y)
        print(f"  x={x:.1f} → 期望收益={expected:.6f}")
    print()
    
    # 例子2：不对称赔率
    print("例子2：不对称赔率情况")
    a, b, c, d = 2.0, -0.5, -0.5, 0.5
    y = 1/3
    
    print(f"赔率设置：a={a}, b={b}, c={c}, d={d}")
    print(f"控股商托价概率：y={y}")
    print()
    
    print("不同买升概率x的期望收益：")
    for x in x_values:
        expected = calculate_expected_return_odds(a, b, c, d, x, y)
        print(f"  x={x:.1f} → 期望收益={expected:.6f}")
    print()

def analyze_realistic_stock_odds():
    """分析现实股票市场的赔率情况"""
    
    print("【现实股票市场分析】")
    print("-" * 60)
    print("在真实股票市场中：")
    print()
    
    print("1. 交易成本：")
    print("   • 手续费、印花税、过户费等")
    print("   • 买卖价差(bid-ask spread)")
    print("   • 这些成本使得期望收益为负")
    print()
    
    print("2. 信息不对称：")
    print("   • 机构投资者vs散户投资者")
    print("   • 内幕信息vs公开信息")
    print("   • 专业分析vs业余判断")
    print()
    
    print("3. 市场操纵：")
    print("   • 大资金操纵股价")
    print("   • 虚假信息传播")
    print("   • 技术性操作")
    print()
    
    # 模拟现实情况
    print("【现实情况模拟】")
    print("-" * 60)
    
    # 考虑交易成本的情况
    transaction_cost = 0.003  # 0.3%的交易成本
    
    print(f"假设交易成本为{transaction_cost*100}%")
    print()
    
    # 不同赔率设置下的分析
    scenarios = [
        ("理想情况(无成本)", 1.0, -1.0, -1.0, 1.0, 0.0),
        ("现实情况(有成本)", 1.0, -1.0, -1.0, 1.0, transaction_cost),
        ("控股商优势", 0.8, -1.2, -1.2, 0.8, transaction_cost),
    ]
    
    y = 0.4  # 控股商40%概率托价
    
    for scenario_name, a, b, c, d, cost in scenarios:
        print(f"{scenario_name}：")
        print(f"  赔率：a={a}, b={b}, c={c}, d={d}")
        print(f"  交易成本：{cost*100}%")
        
        # 计算最优策略
        best_x = 0
        best_return = float('-inf')
        
        for x in np.linspace(0, 1, 101):
            gross_return = calculate_expected_return_odds(a, b, c, d, x, y)
            net_return = gross_return - cost  # 扣除交易成本
            
            if net_return > best_return:
                best_return = net_return
                best_x = x
        
        print(f"  最优策略：x={best_x:.2f}")
        print(f"  最大期望收益：{best_return:.6f}")
        
        if best_return <= 0:
            print("  ❌ 股民无法盈利")
        else:
            print("  ✅ 股民可以盈利")
        print()

def calculate_fair_odds_condition():
    """计算公平赔率的条件"""
    
    print("【公平赔率条件推导】")
    print("-" * 60)
    print("对于期望收益为0的公平博弈：")
    print()
    print("E = xy·净收益1 + (1-x)y·净收益2 + x(1-y)·净收益3 + (1-x)(1-y)·净收益4 = 0")
    print()
    print("这个条件必须对所有x∈[0,1]都成立")
    print("这意味着系数必须满足特定的关系")
    print()
    
    print("【关键结论】")
    print("-" * 60)
    print("1. 如果赔率是真正公平的，那么期望收益确实为0")
    print("2. 但现实中的'赔率'往往不是公平的：")
    print("   • 包含了庄家/交易所的利润")
    print("   • 包含了各种交易成本")
    print("   • 反映了信息不对称")
    print()
    print("3. 在股票市场中：")
    print("   • 散户面临的'赔率'通常是不利的")
    print("   • 机构投资者可能面临更有利的'赔率'")
    print("   • 长期来看，大多数散户确实难以盈利")
    print()

def plot_odds_analysis():
    """绘制赔率分析图表"""
    
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 图1：不同赔率下的期望收益
        x_range = np.linspace(0, 1, 100)
        y_fixed = 0.4
        
        odds_scenarios = [
            (1.0, -1.0, -1.0, 1.0, "对称赔率"),
            (2.0, -0.5, -0.5, 0.5, "买升有利"),
            (0.5, -2.0, -2.0, 2.0, "买跌有利"),
        ]
        
        for a, b, c, d, label in odds_scenarios:
            returns = [calculate_expected_return_odds(a, b, c, d, x, y_fixed) for x in x_range]
            ax1.plot(x_range, returns, label=label, linewidth=2)
        
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_xlabel('x (买升概率)')
        ax1.set_ylabel('期望收益')
        ax1.set_title('不同赔率设置下的期望收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 图2：交易成本的影响
        costs = [0, 0.001, 0.003, 0.005, 0.01]
        a, b, c, d = 1.0, -1.0, -1.0, 1.0
        
        for cost in costs:
            returns = []
            for x in x_range:
                gross_return = calculate_expected_return_odds(a, b, c, d, x, y_fixed)
                net_return = gross_return - cost
                returns.append(net_return)
            ax2.plot(x_range, returns, label=f'成本{cost*100}%', linewidth=2)
        
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_xlabel('x (买升概率)')
        ax2.set_ylabel('净期望收益')
        ax2.set_title('交易成本对期望收益的影响')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 图3：控股商策略y的影响
        y_range = np.linspace(0.1, 0.9, 100)
        x_fixed = 0.5
        a, b, c, d = 1.0, -1.0, -1.0, 1.0
        
        returns_no_cost = [calculate_expected_return_odds(a, b, c, d, x_fixed, y) for y in y_range]
        returns_with_cost = [calculate_expected_return_odds(a, b, c, d, x_fixed, y) - 0.003 for y in y_range]
        
        ax3.plot(y_range, returns_no_cost, label='无交易成本', linewidth=2)
        ax3.plot(y_range, returns_with_cost, label='有交易成本(0.3%)', linewidth=2)
        ax3.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax3.set_xlabel('y (控股商托价概率)')
        ax3.set_ylabel('期望收益')
        ax3.set_title('控股商策略对股民收益的影响')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 图4：盈利区域图
        x_mesh = np.linspace(0, 1, 50)
        y_mesh = np.linspace(0, 1, 50)
        X, Y = np.meshgrid(x_mesh, y_mesh)
        Z = np.zeros_like(X)
        
        a, b, c, d = 1.0, -1.0, -1.0, 1.0
        cost = 0.003
        
        for i in range(len(y_mesh)):
            for j in range(len(x_mesh)):
                gross_return = calculate_expected_return_odds(a, b, c, d, X[i,j], Y[i,j])
                Z[i,j] = gross_return - cost
        
        contour = ax4.contourf(X, Y, Z, levels=20, cmap='RdYlBu_r')
        ax4.contour(X, Y, Z, levels=[0], colors='black', linewidths=2)
        ax4.set_xlabel('x (股民买升概率)')
        ax4.set_ylabel('y (控股商托价概率)')
        ax4.set_title('盈利区域图(考虑交易成本)')
        plt.colorbar(contour, ax=ax4, label='净期望收益')
        
        plt.tight_layout()
        plt.savefig('odds_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为 odds_analysis.png")
        
    except Exception as e:
        print(f"绘图时出现错误: {e}")

def main():
    analyze_fair_odds()
    analyze_realistic_stock_odds()
    calculate_fair_odds_condition()
    plot_odds_analysis()

if __name__ == "__main__":
    main()
