#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析控股商如何通过控制y值使股民无论怎样买卖都赚不到钱
期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d

要使E ≤ 0对所有x ∈ [0,1]都成立，需要分析E关于x的函数性质
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def calculate_expected_value(a, b, c, d, x, y):
    """计算期望值E"""
    return (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d

def analyze_zero_profit_condition(y_min=0.3333333, y_max=0.4):
    """
    分析使股民期望收益为0的条件
    E = (a-b-c+d)xy + (b-d)x + (c-d)y + d
    E = x[(a-b-c+d)y + (b-d)] + (c-d)y + d
    
    设 A = (a-b-c+d)y + (b-d)
    设 B = (c-d)y + d
    则 E = Ax + B
    
    要使E ≤ 0对所有x ∈ [0,1]都成立：
    1. 如果A > 0，则E在x=1时最大，需要A + B ≤ 0
    2. 如果A < 0，则E在x=0时最大，需要B ≤ 0
    3. 如果A = 0，则E = B，需要B ≤ 0
    """
    
    print("="*80)
    print("分析控股商如何使股民无论怎样买卖都赚不到钱")
    print("="*80)
    print(f"分析y值范围：[{y_min}, {y_max}]")
    print()
    
    # 测试几组典型的a,b,c,d值
    test_cases = [
        (50, -1, -1, 50, "极端收益差异"),
        (10, -5, -5, 10, "中等收益差异"),
        (5, -2, -2, 5, "较小收益差异"),
        (20, -10, -5, 15, "不对称收益"),
        (30, -20, -10, 25, "复杂收益结构")
    ]
    
    for a, b, c, d, description in test_cases:
        print(f"【案例：{description}】a={a}, b={b}, c={c}, d={d}")
        print("-" * 60)
        
        # 计算关键系数
        coeff_xy = a - b - c + d  # x和y的交互项系数
        coeff_x = b - d          # x的系数（当y=0时）
        coeff_y = c - d          # y的系数（当x=0时）
        const = d                # 常数项
        
        print(f"E = {coeff_xy}xy + {coeff_x}x + {coeff_y}y + {const}")
        print(f"E = x[{coeff_xy}y + {coeff_x}] + {coeff_y}y + {const}")
        print()
        
        # 分析在给定y范围内的情况
        y_values = np.linspace(y_min, y_max, 100)
        max_E_values = []
        
        for y in y_values:
            A = coeff_xy * y + coeff_x  # x的系数
            B = coeff_y * y + const     # 常数项
            
            # 计算E的最大值（对于x ∈ [0,1]）
            if A > 0:
                max_E = A + B  # x=1时最大
            elif A < 0:
                max_E = B      # x=0时最大
            else:
                max_E = B      # A=0时，E=B
            
            max_E_values.append(max_E)
        
        max_E_values = np.array(max_E_values)
        
        # 找到使max(E) ≤ 0的y值范围
        zero_profit_mask = max_E_values <= 0
        if np.any(zero_profit_mask):
            zero_profit_y = y_values[zero_profit_mask]
            y_range_start = zero_profit_y[0]
            y_range_end = zero_profit_y[-1]
            print(f"使股民无法盈利的y范围：[{y_range_start:.6f}, {y_range_end:.6f}]")
            
            # 检查是否与目标范围重叠
            overlap_start = max(y_range_start, y_min)
            overlap_end = min(y_range_end, y_max)
            if overlap_start <= overlap_end:
                print(f"与目标范围[{y_min}, {y_max}]的重叠：[{overlap_start:.6f}, {overlap_end:.6f}]")
            else:
                print(f"与目标范围[{y_min}, {y_max}]无重叠")
        else:
            print("在给定y范围内，股民总能找到盈利策略")
        
        # 计算边界值
        print(f"在y={y_min}时的最大E：{max_E_values[0]:.6f}")
        print(f"在y={y_max}时的最大E：{max_E_values[-1]:.6f}")
        print()

def find_critical_y_values():
    """
    寻找关键的y值，使得股民期望收益刚好为0
    """
    print("【寻找关键y值】")
    print("-" * 60)
    
    # 对于一般情况，分析E=0的条件
    print("一般分析：")
    print("E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print("设 A = a-b-c+d, B = b-d, C = c-d, D = d")
    print("则 E = Axy + Bx + Cy + D")
    print("E = x(Ay + B) + Cy + D")
    print()
    
    print("要使E ≤ 0对所有x ∈ [0,1]成立：")
    print("1. 当Ay + B ≥ 0时，E在x=1处最大，需要：(Ay + B) + Cy + D ≤ 0")
    print("   即：(A + C)y + (B + D) ≤ 0")
    print("   即：y ≤ -(B + D)/(A + C) = -(b-d+d)/(a-b-c+d+c-d) = -b/(a-b)")
    print()
    print("2. 当Ay + B ≤ 0时，E在x=0处最大，需要：Cy + D ≤ 0")
    print("   即：y ≤ -D/C = -d/(c-d)")
    print()
    print("3. 临界条件Ay + B = 0，即：y = -B/A = -(b-d)/(a-b-c+d)")
    print()
    
    # 具体计算几个例子
    examples = [
        (50, -1, -1, 50),
        (10, -5, -5, 10),
        (20, -10, -5, 15)
    ]
    
    for a, b, c, d in examples:
        print(f"例子：a={a}, b={b}, c={c}, d={d}")
        A = a - b - c + d
        B = b - d
        C = c - d
        D = d
        
        # 计算关键y值
        if A != 0:
            y_critical = -B / A
            print(f"  临界y值：{y_critical:.6f}")
        
        if A + C != 0:
            y_upper = -(B + D) / (A + C)
            print(f"  上界y值：{y_upper:.6f}")
        
        if C != 0:
            y_lower = -D / C
            print(f"  下界y值：{y_lower:.6f}")
        
        print()

def plot_analysis():
    """绘制分析图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 测试案例
    a, b, c, d = 50, -1, -1, 50
    
    # 图1：不同y值下E关于x的函数
    ax1 = axes[0, 0]
    x_vals = np.linspace(0, 1, 100)
    y_test_vals = [0.2, 0.33, 0.4, 0.5]
    
    for y in y_test_vals:
        E_vals = [calculate_expected_value(a, b, c, d, x, y) for x in x_vals]
        ax1.plot(x_vals, E_vals, label=f'y={y}')
    
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_xlabel('x (股民买升概率)')
    ax1.set_ylabel('期望收益 E')
    ax1.set_title('不同y值下期望收益E关于x的变化')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2：y值对最大E的影响
    ax2 = axes[0, 1]
    y_range = np.linspace(0.1, 0.8, 200)
    max_E_range = []
    
    for y in y_range:
        # 计算在该y值下，E关于x的最大值
        A = (a - b - c + d) * y + (b - d)
        B = (c - d) * y + d
        
        if A > 0:
            max_E = A + B
        else:
            max_E = B
        
        max_E_range.append(max_E)
    
    ax2.plot(y_range, max_E_range, 'b-', linewidth=2)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax2.axvspan(0.3333333, 0.4, alpha=0.3, color='yellow', label='目标区间')
    ax2.set_xlabel('y (控股商托价概率)')
    ax2.set_ylabel('最大期望收益 E')
    ax2.set_title('控股商策略y对股民最大收益的影响')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 图3：零收益边界
    ax3 = axes[1, 0]
    
    # 计算使E=0的边界线
    y_boundary = np.linspace(0.1, 0.8, 100)
    x_boundary_upper = []
    x_boundary_lower = []
    
    for y in y_boundary:
        A = (a - b - c + d) * y + (b - d)
        B = (c - d) * y + d
        
        if A != 0:
            x_zero = -B / A
            if 0 <= x_zero <= 1:
                x_boundary_upper.append(x_zero)
                x_boundary_lower.append(x_zero)
            else:
                x_boundary_upper.append(np.nan)
                x_boundary_lower.append(np.nan)
        else:
            x_boundary_upper.append(np.nan)
            x_boundary_lower.append(np.nan)
    
    ax3.plot(y_boundary, x_boundary_upper, 'g-', linewidth=2, label='E=0边界')
    ax3.axvspan(0.3333333, 0.4, alpha=0.3, color='yellow', label='目标区间')
    ax3.set_xlabel('y (控股商托价概率)')
    ax3.set_ylabel('x (股民买升概率)')
    ax3.set_title('零收益边界线')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(0.1, 0.8)
    ax3.set_ylim(0, 1)
    
    # 图4：3D收益面
    ax4 = axes[1, 1]
    
    # 创建网格
    x_3d = np.linspace(0, 1, 20)
    y_3d = np.linspace(0.2, 0.6, 20)
    X, Y = np.meshgrid(x_3d, y_3d)
    Z = np.zeros_like(X)
    
    for i in range(len(y_3d)):
        for j in range(len(x_3d)):
            Z[i, j] = calculate_expected_value(a, b, c, d, X[i, j], Y[i, j])
    
    contour = ax4.contour(X, Y, Z, levels=[-10, -5, 0, 5, 10, 20], colors='black', alpha=0.6)
    ax4.clabel(contour, inline=True, fontsize=8)
    
    # 填充零收益区域
    ax4.contourf(X, Y, Z, levels=[-100, 0], colors=['lightcoral'], alpha=0.5)
    ax4.contourf(X, Y, Z, levels=[0, 100], colors=['lightgreen'], alpha=0.5)
    
    ax4.axhspan(0.3333333, 0.4, alpha=0.3, color='yellow')
    ax4.set_xlabel('x (股民买升概率)')
    ax4.set_ylabel('y (控股商托价概率)')
    ax4.set_title('期望收益等高线图')
    
    plt.tight_layout()
    plt.savefig('zero_profit_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    # 分析零收益条件
    analyze_zero_profit_condition()
    
    # 寻找关键y值
    find_critical_y_values()
    
    # 绘制分析图表
    try:
        plot_analysis()
        print("图表已保存为 zero_profit_analysis.png")
    except Exception as e:
        print(f"绘图时出现错误: {e}")

if __name__ == "__main__":
    main()
