#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析特定参数 a=3, b=-2, c=-2, d=1 的情况
"""

import numpy as np
import matplotlib.pyplot as plt

def calculate_expected_value(a, b, c, d, x, y):
    """计算期望值E"""
    return (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d

def analyze_specific_case():
    """分析 a=3, b=-2, c=-2, d=1 的情况"""
    
    # 参数设置
    a, b, c, d = 3, -2, -2, 1
    
    print("="*80)
    print(f"分析特定参数：a={a}, b={b}, c={c}, d={d}")
    print("="*80)
    
    # 计算系数
    A = a - b - c + d  # 3-(-2)-(-2)+1 = 8
    B = b - d          # -2-1 = -3
    C = c - d          # -2-1 = -3
    D = d              # 1
    
    print("期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print(f"E = ({a}-({b})-({c})+{d})xy + ({b}-{d})x + ({c}-{d})y + {d}")
    print(f"E = {A}xy + {B}x + {C}y + {D}")
    print(f"E = x({A}y + {B}) + {C}y + {D}")
    print()
    
    # 分析在不同y值下的情况
    print("【不同y值下的分析】")
    print("-" * 60)
    
    y_values = [1/3, 0.35, 0.4, 0.45, 0.5]
    
    for y in y_values:
        coeff_x = A * y + B  # x的系数
        const_term = C * y + D  # 常数项
        
        print(f"当y = {y:.6f}时：")
        print(f"  E = x({coeff_x:.6f}) + {const_term:.6f}")
        
        # 确定E的最大值和最小值
        if coeff_x > 0:
            max_E = coeff_x + const_term  # x=1时最大
            min_E = const_term            # x=0时最小
            optimal_x_max = 1.0
            optimal_x_min = 0.0
        elif coeff_x < 0:
            max_E = const_term            # x=0时最大
            min_E = coeff_x + const_term  # x=1时最小
            optimal_x_max = 0.0
            optimal_x_min = 1.0
        else:  # coeff_x = 0
            max_E = min_E = const_term
            optimal_x_max = optimal_x_min = "任意值"
        
        print(f"  最大E = {max_E:.6f} (当x = {optimal_x_max}时)")
        print(f"  最小E = {min_E:.6f} (当x = {optimal_x_min}时)")
        
        if max_E <= 0:
            print(f"  ✓ 股民无论如何买卖都无法盈利")
        elif min_E >= 0:
            print(f"  ✗ 股民无论如何买卖都能盈利")
        else:
            print(f"  ◐ 股民可以通过选择合适的x来盈利")
        print()
    
    # 重点分析目标范围 [1/3, 0.4]
    print("【目标范围 [1/3, 0.4] 详细分析】")
    print("-" * 60)
    
    y_min, y_max = 1/3, 0.4
    
    for y in [y_min, y_max]:
        print(f"在y = {y:.6f}时：")
        
        # 计算所有x值的E
        x_values = np.linspace(0, 1, 11)
        E_values = []
        
        for x in x_values:
            E = calculate_expected_value(a, b, c, d, x, y)
            E_values.append(E)
            print(f"  x = {x:.1f}, E = {E:.6f}")
        
        max_E_in_range = max(E_values)
        min_E_in_range = min(E_values)
        
        print(f"  范围内最大E = {max_E_in_range:.6f}")
        print(f"  范围内最小E = {min_E_in_range:.6f}")
        print()
    
    # 寻找使E=0的临界点
    print("【临界点分析】")
    print("-" * 60)
    
    # E = 0的条件：x(Ay + B) + Cy + D = 0
    # 即：x = -(Cy + D)/(Ay + B)，当Ay + B ≠ 0时
    
    print("E = 0的条件分析：")
    print("x(Ay + B) + Cy + D = 0")
    print("当Ay + B ≠ 0时，x = -(Cy + D)/(Ay + B)")
    print()
    
    for y in [y_min, y_max]:
        coeff_x = A * y + B
        const_term = C * y + D
        
        if abs(coeff_x) > 1e-10:  # 避免除零
            x_zero = -const_term / coeff_x
            print(f"当y = {y:.6f}时，E = 0的x值：{x_zero:.6f}")
            
            if 0 <= x_zero <= 1:
                print(f"  ✓ x = {x_zero:.6f} 在有效范围内，此时E = 0")
                # 验证
                E_verify = calculate_expected_value(a, b, c, d, x_zero, y)
                print(f"  验证：E = {E_verify:.10f}")
            else:
                print(f"  ✗ x = {x_zero:.6f} 超出[0,1]范围")
        else:
            if abs(const_term) < 1e-10:
                print(f"当y = {y:.6f}时，对所有x都有E = 0")
            else:
                print(f"当y = {y:.6f}时，不存在使E = 0的x值")
        print()
    
    # 绘制图表
    print("【图表分析】")
    print("-" * 60)
    
    try:
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 图1：不同y值下E关于x的函数
        x_plot = np.linspace(0, 1, 100)
        y_test = [1/3, 0.35, 0.4, 0.45, 0.5]
        
        for y in y_test:
            E_plot = [calculate_expected_value(a, b, c, d, x, y) for x in x_plot]
            ax1.plot(x_plot, E_plot, label=f'y={y:.3f}')
        
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='E=0')
        ax1.set_xlabel('x (股民买升概率)')
        ax1.set_ylabel('期望收益 E')
        ax1.set_title(f'参数a={a},b={b},c={c},d={d}下E关于x的变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 图2：y对最大E的影响
        y_range = np.linspace(0.2, 0.6, 200)
        max_E_range = []
        
        for y in y_range:
            coeff_x = A * y + B
            const_term = C * y + D
            
            if coeff_x > 0:
                max_E = coeff_x + const_term
            else:
                max_E = const_term
            
            max_E_range.append(max_E)
        
        ax2.plot(y_range, max_E_range, 'b-', linewidth=2)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax2.axvspan(1/3, 0.4, alpha=0.3, color='yellow', label='目标区间')
        ax2.set_xlabel('y (控股商托价概率)')
        ax2.set_ylabel('最大期望收益 E')
        ax2.set_title('控股商策略y对股民最大收益的影响')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 图3：热力图
        x_heat = np.linspace(0, 1, 50)
        y_heat = np.linspace(0.2, 0.6, 50)
        X_heat, Y_heat = np.meshgrid(x_heat, y_heat)
        Z_heat = np.zeros_like(X_heat)
        
        for i in range(len(y_heat)):
            for j in range(len(x_heat)):
                Z_heat[i, j] = calculate_expected_value(a, b, c, d, X_heat[i, j], Y_heat[i, j])
        
        im = ax3.imshow(Z_heat, extent=[0, 1, 0.2, 0.6], aspect='auto', origin='lower', cmap='RdYlBu_r')
        ax3.contour(X_heat, Y_heat, Z_heat, levels=[0], colors='black', linewidths=2)
        ax3.axhspan(1/3, 0.4, alpha=0.3, color='white', edgecolor='black', linewidth=2)
        ax3.set_xlabel('x (股民买升概率)')
        ax3.set_ylabel('y (控股商托价概率)')
        ax3.set_title('期望收益E的热力图')
        plt.colorbar(im, ax=ax3, label='期望收益 E')
        
        # 图4：目标区间内的详细分析
        x_detail = np.linspace(0, 1, 100)
        y_detail = [1/3, 0.35, 0.4]
        
        for y in y_detail:
            E_detail = [calculate_expected_value(a, b, c, d, x, y) for x in x_detail]
            ax4.plot(x_detail, E_detail, label=f'y={y:.3f}', linewidth=2)
        
        ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='E=0')
        ax4.fill_between(x_detail, -10, 0, alpha=0.2, color='red', label='亏损区域')
        ax4.fill_between(x_detail, 0, 10, alpha=0.2, color='green', label='盈利区域')
        ax4.set_xlabel('x (股民买升概率)')
        ax4.set_ylabel('期望收益 E')
        ax4.set_title('目标区间内的详细分析')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(-2, 2)
        
        plt.tight_layout()
        plt.savefig('specific_case_analysis.png', dpi=300, bbox_inches='tight')
        print("图表已保存为 specific_case_analysis.png")
        
    except Exception as e:
        print(f"绘图时出现错误: {e}")
    
    # 结论
    print("\n【结论】")
    print("-" * 60)
    print(f"对于参数 a={a}, b={b}, c={c}, d={d}：")
    
    # 检查在目标范围内是否能实现零收益
    y_target_range = np.linspace(1/3, 0.4, 100)
    can_achieve_zero_profit = True
    
    for y in y_target_range:
        coeff_x = A * y + B
        const_term = C * y + D
        
        max_E = max(const_term, coeff_x + const_term) if coeff_x > 0 else const_term
        
        if max_E > 0.001:  # 允许小误差
            can_achieve_zero_profit = False
            break
    
    if can_achieve_zero_profit:
        print("✓ 在y ∈ [1/3, 0.4]范围内，可以使股民无论怎样买卖都赚不到钱")
    else:
        print("✗ 在y ∈ [1/3, 0.4]范围内，股民仍然可以找到盈利策略")
    
    # 计算具体的最大收益
    max_E_at_third = max(C/3 + D, A/3 + B + C/3 + D) if A/3 + B > 0 else C/3 + D
    max_E_at_04 = max(0.4*C + D, 0.4*A + B + 0.4*C + D) if 0.4*A + B > 0 else 0.4*C + D
    
    print(f"在y = 1/3时，股民最大收益：{max_E_at_third:.6f}")
    print(f"在y = 0.4时，股民最大收益：{max_E_at_04:.6f}")

def main():
    analyze_specific_case()

if __name__ == "__main__":
    main()
