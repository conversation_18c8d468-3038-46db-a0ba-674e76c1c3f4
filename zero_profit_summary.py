#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零收益策略总结报告
"""

def calculate_expected_value(a, b, c, d, x, y):
    """计算期望值E"""
    return (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d

def main():
    print("="*80)
    print("🎯 零收益策略分析报告")
    print("="*80)
    print()
    
    # 参数设置
    a, b, c, d = 3, -2, -2, 1
    y_min, y_max = 1/3, 0.4
    
    print("【问题背景】")
    print("-" * 60)
    print("目标：寻找参数组合，使得当控股商托价概率y在[1/3, 0.4]范围内时，")
    print("股民无论采用什么买卖策略（任意x值），期望收益都≤0")
    print()
    
    print("【成功案例】")
    print("-" * 60)
    print(f"✅ 找到有效参数组合：a={a}, b={b}, c={c}, d={d}")
    print()
    
    # 计算系数
    A = a - b - c + d  # 8
    B = b - d          # -3
    C = c - d          # -3
    D = d              # 1
    
    print("【数学分析】")
    print("-" * 60)
    print("期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print(f"代入参数：E = {A}xy + {B}x + {C}y + {D}")
    print(f"重写为：E = x({A}y + {B}) + {C}y + {D}")
    print()
    
    print("关键洞察：")
    print("- E关于x是线性函数，系数为(8y - 3)")
    print("- 当y = 3/8 = 0.375时，系数为0，E与x无关")
    print("- 当y < 3/8时，系数<0，E在x=0时最大")
    print("- 当y > 3/8时，系数>0，E在x=1时最大")
    print()
    
    print("【关键y值验证】")
    print("-" * 60)
    
    # 验证边界点
    test_y_values = [1/3, 0.375, 0.4]
    
    for y in test_y_values:
        coeff_x = A * y + B
        const_term = C * y + D
        
        print(f"当y = {y:.6f}时：")
        print(f"  E = x({coeff_x:.6f}) + {const_term:.6f}")
        
        if abs(coeff_x) < 1e-10:  # 系数接近0
            print(f"  E = {const_term:.6f} (与x无关)")
            max_E = const_term
        elif coeff_x > 0:
            max_E = coeff_x + const_term  # x=1时最大
            print(f"  最大E = {max_E:.6f} (当x=1时)")
        else:
            max_E = const_term  # x=0时最大
            print(f"  最大E = {max_E:.6f} (当x=0时)")
        
        if max_E <= 0.001:
            print("  ✅ 股民无法盈利")
        else:
            print("  ❌ 股民可以盈利")
        print()
    
    print("【完整验证】")
    print("-" * 60)
    
    # 验证整个区间
    import numpy as np
    y_range = np.linspace(y_min, y_max, 100)
    all_non_positive = True
    
    for y in y_range:
        coeff_x = A * y + B
        const_term = C * y + D
        
        # 计算最大E
        if coeff_x > 0:
            max_E = coeff_x + const_term
        else:
            max_E = const_term
        
        if max_E > 0.001:  # 允许小误差
            all_non_positive = False
            break
    
    if all_non_positive:
        print("✅ 验证成功：在整个y∈[1/3, 0.4]区间内，股民最大期望收益都≤0")
    else:
        print("❌ 验证失败：存在y值使股民可以盈利")
    print()
    
    print("【具体数值验证】")
    print("-" * 60)
    
    # 详细验证几个关键点
    verification_points = [
        (y_min, 0.0),    # y=1/3, x=0
        (y_min, 1.0),    # y=1/3, x=1
        (y_max, 0.0),    # y=0.4, x=0
        (y_max, 1.0),    # y=0.4, x=1
        (0.375, 0.5),    # y=3/8, x=0.5
    ]
    
    print("关键点验证：")
    for y, x in verification_points:
        E = calculate_expected_value(a, b, c, d, x, y)
        print(f"  y={y:.6f}, x={x:.1f} → E={E:.6f}")
    print()
    
    print("【经济学解释】")
    print("-" * 60)
    print("这个参数组合的经济含义：")
    print(f"• a={a}: 股民买升且控股商托价时，收益+3元")
    print(f"• b={b}: 股民买跌且控股商托价时，亏损-2元")
    print(f"• c={c}: 股民买升且控股商压价时，亏损-2元")
    print(f"• d={d}: 股民买跌且控股商压价时，收益+1元")
    print()
    print("关键特征：")
    print("• 买升的收益差异大(3 vs -2)，但风险也大")
    print("• 买跌的收益差异小(-2 vs 1)，相对稳定")
    print("• 当控股商托价概率在[1/3, 0.4]时，这种不对称性")
    print("  恰好使得股民无论选择什么策略都无法获得正收益")
    print()
    
    print("【博弈论意义】")
    print("-" * 60)
    print("这个发现证明了：")
    print("1. 存在参数组合使控股商能够完全控制股民收益")
    print("2. 控股商通过调节托价概率到特定区间[1/3, 0.4]")
    print("3. 可以确保股民无论采用何种策略都无法盈利")
    print("4. 这是一个强势的控股商策略，完全压制了股民的盈利空间")
    print()
    
    print("【实际应用】")
    print("-" * 60)
    print("如果控股商能够：")
    print("• 设计收益结构为 a=3, b=-2, c=-2, d=1")
    print("• 控制托价概率在 33.33% 到 40% 之间")
    print("• 那么无论股民如何调整买升/买跌的概率")
    print("• 股民的期望收益都将为零或负数")
    print()
    print("这为控股商提供了一个理论上的'零和博弈'策略框架。")

if __name__ == "__main__":
    main()
