#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的赔率分析：正确理解赔率在股票博弈中的含义
"""

def calculate_expected_value_with_odds(a, b, c, d, x, y):
    """
    正确的赔率期望值计算
    
    在股票博弈中，a,b,c,d如果是赔率，应该理解为：
    - 投入1元，在相应情况下能获得的总回报（包含本金）
    - 净收益 = 总回报 - 投入本金 = 赔率 - 1
    
    但更常见的理解是直接作为净收益率
    """
    
    # 四种情况的期望净收益
    # 情况1：股民买升，控股商托价 (概率: x*y, 净收益: a)
    # 情况2：股民买跌，控股商托价 (概率: (1-x)*y, 净收益: b)  
    # 情况3：股民买升，控股商压价 (概率: x*(1-y), 净收益: c)
    # 情况4：股民买跌，控股商压价 (概率: (1-x)*(1-y), 净收益: d)
    
    expected_return = (x * y * a + 
                      (1-x) * y * b + 
                      x * (1-y) * c + 
                      (1-x) * (1-y) * d)
    
    return expected_return

def analyze_stock_market_reality():
    """分析股票市场的现实情况"""
    
    print("="*80)
    print("🏦 股票市场赔率现实分析")
    print("="*80)
    print()
    
    print("【股票市场的'赔率'理解】")
    print("-" * 60)
    print("在股票投资中，a、b、c、d更应该理解为：")
    print("• 不同情况下的收益率（而非传统博彩赔率）")
    print("• 正值表示盈利，负值表示亏损")
    print("• 单位通常是百分比或倍数")
    print()
    
    print("【现实股票市场特征】")
    print("-" * 60)
    print("1. 零和博弈特性：")
    print("   • 股票市场本质上不是零和博弈")
    print("   • 但短期投机交易接近零和博弈")
    print("   • 扣除交易成本后，散户整体是负和博弈")
    print()
    
    print("2. 信息不对称：")
    print("   • 机构vs散户的信息差")
    print("   • 专业分析vs情绪化交易")
    print("   • 资金规模带来的优势")
    print()
    
    print("3. 交易成本：")
    print("   • 手续费：0.1%-0.3%")
    print("   • 印花税：0.1%（卖出时）")
    print("   • 价差成本：0.1%-0.5%")
    print("   • 总成本：单次交易约0.3%-1%")
    print()
    
    # 现实参数分析
    print("【现实参数分析】")
    print("-" * 60)
    
    # 情况1：理想化的公平市场
    print("情况1：理想化公平市场")
    a, b, c, d = 0.05, -0.05, -0.05, 0.05  # ±5%的对称收益
    transaction_cost = 0.005  # 0.5%交易成本
    
    print(f"收益率设置：a={a*100}%, b={b*100}%, c={c*100}%, d={d*100}%")
    print(f"交易成本：{transaction_cost*100}%")
    
    y_values = [0.3, 0.4, 0.5, 0.6, 0.7]
    
    for y in y_values:
        print(f"\n当控股商托价概率y={y}时：")
        
        best_x = 0
        best_net_return = float('-inf')
        
        for x_test in [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]:
            gross_return = calculate_expected_value_with_odds(a, b, c, d, x_test, y)
            net_return = gross_return - transaction_cost
            
            if net_return > best_net_return:
                best_net_return = net_return
                best_x = x_test
        
        print(f"  最优策略：x={best_x} (买升概率{best_x*100}%)")
        print(f"  毛收益率：{calculate_expected_value_with_odds(a, b, c, d, best_x, y)*100:.2f}%")
        print(f"  净收益率：{best_net_return*100:.2f}%")
        
        if best_net_return <= 0:
            print("  ❌ 扣除成本后无法盈利")
        else:
            print("  ✅ 扣除成本后仍可盈利")
    
    print("\n" + "="*60)
    
    # 情况2：现实的不对称市场
    print("情况2：现实不对称市场（散户劣势）")
    a, b, c, d = 0.03, -0.07, -0.08, 0.02  # 不对称收益，反映散户劣势
    transaction_cost = 0.008  # 0.8%交易成本（散户面临更高成本）
    
    print(f"收益率设置：a={a*100}%, b={b*100}%, c={c*100}%, d={d*100}%")
    print(f"交易成本：{transaction_cost*100}%")
    
    for y in y_values:
        print(f"\n当控股商托价概率y={y}时：")
        
        best_x = 0
        best_net_return = float('-inf')
        
        for x_test in [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]:
            gross_return = calculate_expected_value_with_odds(a, b, c, d, x_test, y)
            net_return = gross_return - transaction_cost
            
            if net_return > best_net_return:
                best_net_return = net_return
                best_x = x_test
        
        print(f"  最优策略：x={best_x} (买升概率{best_x*100}%)")
        print(f"  毛收益率：{calculate_expected_value_with_odds(a, b, c, d, best_x, y)*100:.2f}%")
        print(f"  净收益率：{best_net_return*100:.2f}%")
        
        if best_net_return <= 0:
            print("  ❌ 扣除成本后无法盈利")
        else:
            print("  ✅ 扣除成本后仍可盈利")

def analyze_zero_sum_condition():
    """分析零和博弈条件"""
    
    print("\n" + "="*80)
    print("🎯 零和博弈条件分析")
    print("="*80)
    print()
    
    print("【零和博弈的数学条件】")
    print("-" * 60)
    print("要使股民期望收益为0（零和博弈），需要：")
    print("E = xy·a + (1-x)y·b + x(1-y)·c + (1-x)(1-y)·d = 0")
    print()
    print("重新整理：")
    print("E = x[y(a-b) + (1-y)(c-d)] + y·b + (1-y)·d = 0")
    print()
    print("要使这个等式对所有x∈[0,1]都成立，必须：")
    print("1. y(a-b) + (1-y)(c-d) = 0  （x的系数为0）")
    print("2. y·b + (1-y)·d = 0         （常数项为0）")
    print()
    
    print("【求解零和条件】")
    print("-" * 60)
    print("从条件2：y·b + (1-y)·d = 0")
    print("解得：y = -d/(b-d)")
    print()
    print("代入条件1验证是否成立...")
    print()
    
    # 具体例子验证
    print("【具体例子验证】")
    print("-" * 60)
    
    examples = [
        (0.05, -0.05, -0.05, 0.05, "对称情况"),
        (0.06, -0.04, -0.04, 0.06, "轻微不对称"),
        (0.08, -0.02, -0.02, 0.08, "强不对称"),
    ]
    
    for a, b, c, d, desc in examples:
        print(f"{desc}：a={a}, b={b}, c={c}, d={d}")
        
        # 计算理论零和y值
        if b != d:
            y_zero = -d / (b - d)
            print(f"  理论零和y值：{y_zero:.4f}")
            
            if 0 <= y_zero <= 1:
                print(f"  ✅ y值在有效范围内")
                
                # 验证此时的期望收益
                for x_test in [0.0, 0.5, 1.0]:
                    E = calculate_expected_value_with_odds(a, b, c, d, x_test, y_zero)
                    print(f"    x={x_test}时，E={E:.6f}")
            else:
                print(f"  ❌ y值超出[0,1]范围")
        else:
            print(f"  ⚠️  b=d，无法确定唯一零和y值")
        print()

def main():
    analyze_stock_market_reality()
    analyze_zero_sum_condition()
    
    print("\n" + "="*80)
    print("🎯 总结")
    print("="*80)
    print()
    print("【关键结论】")
    print("-" * 60)
    print("1. 如果a,b,c,d是收益率（赔率），股票投资的盈利性取决于：")
    print("   • 收益率的设置是否公平")
    print("   • 交易成本的高低")
    print("   • 信息获取能力")
    print()
    print("2. 在现实股票市场中：")
    print("   • 散户面临不利的'赔率'结构")
    print("   • 高交易成本进一步侵蚀收益")
    print("   • 信息不对称使散户处于劣势")
    print()
    print("3. 因此，大多数散户确实难以在股票市场持续盈利")
    print("   这验证了'股民赚不到钱'的观察")
    print()
    print("4. 但这不是绝对的：")
    print("   • 长期价值投资可能盈利")
    print("   • 专业投资者有更好的'赔率'")
    print("   • 降低交易成本可以改善结果")

if __name__ == "__main__":
    main()
