#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析股民观察Y值（控股商托价概率）的实际作用
"""

import numpy as np
import matplotlib.pyplot as plt

def calculate_expected_return(a, b, c, d, x, y):
    """计算期望收益"""
    return (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d

def find_optimal_strategy(a, b, c, d, y, transaction_cost=0):
    """
    给定Y值，找到股民的最优策略x
    """
    best_x = 0
    best_return = float('-inf')
    
    # 遍历所有可能的x值
    x_values = np.linspace(0, 1, 101)
    
    for x in x_values:
        gross_return = calculate_expected_return(a, b, c, d, x, y)
        net_return = gross_return - transaction_cost
        
        if net_return > best_return:
            best_return = net_return
            best_x = x
    
    return best_x, best_return

def analyze_y_observation_value():
    """分析观察Y值的价值"""
    
    print("="*80)
    print("🔍 股民观察Y值的实际作用分析")
    print("="*80)
    print()
    
    print("【理论分析】")
    print("-" * 60)
    print("期望收益公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print("重写为：E = x[(a-b-c+d)y + (b-d)] + (c-d)y + d")
    print()
    print("关键观察：")
    print("• E关于x是线性函数")
    print("• x的系数是：(a-b-c+d)y + (b-d)")
    print("• 常数项是：(c-d)y + d")
    print()
    print("如果股民能准确观察到Y值，就能：")
    print("1. 计算x的系数，判断应该买升还是买跌")
    print("2. 计算最优的买升概率x")
    print("3. 预测期望收益")
    print()
    
    # 具体案例分析
    print("【具体案例分析】")
    print("-" * 60)
    
    # 案例1：对称市场
    print("案例1：相对公平的市场")
    a, b, c, d = 0.05, -0.05, -0.05, 0.05
    transaction_cost = 0.005
    
    print(f"参数：a={a*100}%, b={b*100}%, c={c*100}%, d={d*100}%")
    print(f"交易成本：{transaction_cost*100}%")
    print()
    
    y_scenarios = [0.3, 0.4, 0.5, 0.6, 0.7]
    
    print("不同Y值下的最优策略：")
    print("Y值\t最优x\t买升概率\t净收益率\t策略建议")
    print("-" * 60)
    
    for y in y_scenarios:
        optimal_x, net_return = find_optimal_strategy(a, b, c, d, y, transaction_cost)
        
        if optimal_x < 0.1:
            strategy = "强烈买跌"
        elif optimal_x < 0.3:
            strategy = "偏向买跌"
        elif optimal_x < 0.7:
            strategy = "均衡策略"
        elif optimal_x < 0.9:
            strategy = "偏向买升"
        else:
            strategy = "强烈买升"
        
        print(f"{y:.1f}\t{optimal_x:.2f}\t{optimal_x*100:5.1f}%\t\t{net_return*100:6.2f}%\t\t{strategy}")
    
    print()
    
    # 案例2：不对称市场（散户劣势）
    print("案例2：散户劣势市场")
    a, b, c, d = 0.03, -0.07, -0.08, 0.02
    transaction_cost = 0.008
    
    print(f"参数：a={a*100}%, b={b*100}%, c={c*100}%, d={d*100}%")
    print(f"交易成本：{transaction_cost*100}%")
    print()
    
    print("不同Y值下的最优策略：")
    print("Y值\t最优x\t买升概率\t净收益率\t策略建议")
    print("-" * 60)
    
    for y in y_scenarios:
        optimal_x, net_return = find_optimal_strategy(a, b, c, d, y, transaction_cost)
        
        if optimal_x < 0.1:
            strategy = "强烈买跌"
        elif optimal_x < 0.3:
            strategy = "偏向买跌"
        elif optimal_x < 0.7:
            strategy = "均衡策略"
        elif optimal_x < 0.9:
            strategy = "偏向买升"
        else:
            strategy = "强烈买升"
        
        print(f"{y:.1f}\t{optimal_x:.2f}\t{optimal_x*100:5.1f}%\t\t{net_return*100:6.2f}%\t\t{strategy}")
    
    print()

def analyze_y_prediction_difficulty():
    """分析预测Y值的难度"""
    
    print("【观察Y值的实际困难】")
    print("-" * 60)
    print("1. 信息获取困难：")
    print("   • 控股商的真实意图难以观察")
    print("   • 托价/压价行为可能很隐蔽")
    print("   • 市场噪音干扰判断")
    print()
    
    print("2. 时间滞后性：")
    print("   • 观察到Y值时，机会可能已经错过")
    print("   • 控股商策略可能随时改变")
    print("   • 历史Y值不能预测未来")
    print()
    
    print("3. 观察成本：")
    print("   • 需要专业分析能力")
    print("   • 需要大量时间和资源")
    print("   • 可能需要内幕信息（违法）")
    print()

def calculate_y_observation_value():
    """计算观察Y值的经济价值"""
    
    print("【观察Y值的经济价值计算】")
    print("-" * 60)
    
    # 假设场景
    a, b, c, d = 0.05, -0.05, -0.05, 0.05
    transaction_cost = 0.005
    
    print("假设场景：相对公平市场")
    print(f"参数：a={a*100}%, b={b*100}%, c={c*100}%, d={d*100}%")
    print()
    
    # 情况1：不知道Y值，随机策略
    print("情况1：不观察Y值，采用随机策略（x=0.5）")
    random_returns = []
    y_values = np.linspace(0.1, 0.9, 9)
    
    for y in y_values:
        gross_return = calculate_expected_return(a, b, c, d, 0.5, y)
        net_return = gross_return - transaction_cost
        random_returns.append(net_return)
    
    avg_random_return = np.mean(random_returns)
    print(f"平均净收益率：{avg_random_return*100:.2f}%")
    print()
    
    # 情况2：完美观察Y值，采用最优策略
    print("情况2：完美观察Y值，采用最优策略")
    optimal_returns = []
    
    for y in y_values:
        optimal_x, net_return = find_optimal_strategy(a, b, c, d, y, transaction_cost)
        optimal_returns.append(net_return)
    
    avg_optimal_return = np.mean(optimal_returns)
    print(f"平均净收益率：{avg_optimal_return*100:.2f}%")
    print()
    
    # 计算观察Y值的价值
    observation_value = avg_optimal_return - avg_random_return
    print(f"观察Y值的价值：{observation_value*100:.2f}%")
    
    if observation_value > 0:
        print("✅ 观察Y值有正价值")
    else:
        print("❌ 观察Y值无价值")
    print()

def analyze_practical_implications():
    """分析实际应用意义"""
    
    print("【实际应用分析】")
    print("-" * 60)
    
    print("1. 对于普通散户：")
    print("   • 观察Y值的成本通常大于收益")
    print("   • 缺乏专业分析能力")
    print("   • 容易被虚假信号误导")
    print("   • 建议：采用长期投资策略，减少短期博弈")
    print()
    
    print("2. 对于专业投资者：")
    print("   • 有能力和资源观察市场信号")
    print("   • 可以通过技术分析判断主力意图")
    print("   • 有更低的交易成本")
    print("   • 观察Y值可能有一定价值")
    print()
    
    print("3. 对于量化交易者：")
    print("   • 可以通过算法模型估计Y值")
    print("   • 能够快速响应Y值变化")
    print("   • 有系统化的风险管理")
    print("   • 观察Y值最有价值")
    print()

def simulate_y_observation_scenarios():
    """模拟不同Y值观察场景"""
    
    print("【模拟分析：不同观察能力的收益对比】")
    print("-" * 60)
    
    # 市场参数
    a, b, c, d = 0.04, -0.06, -0.06, 0.04
    transaction_cost = 0.006
    
    # 模拟100个交易周期，Y值随机变化
    np.random.seed(42)
    y_sequence = np.random.uniform(0.2, 0.8, 100)
    
    # 策略1：完全不观察Y，固定策略x=0.5
    strategy1_returns = []
    for y in y_sequence:
        gross_return = calculate_expected_return(a, b, c, d, 0.5, y)
        net_return = gross_return - transaction_cost
        strategy1_returns.append(net_return)
    
    # 策略2：完美观察Y，每次都采用最优策略
    strategy2_returns = []
    for y in y_sequence:
        optimal_x, net_return = find_optimal_strategy(a, b, c, d, y, transaction_cost)
        strategy2_returns.append(net_return)
    
    # 策略3：部分观察Y（有50%概率观察错误）
    strategy3_returns = []
    for i, y in enumerate(y_sequence):
        if np.random.random() < 0.5:  # 50%概率观察正确
            optimal_x, net_return = find_optimal_strategy(a, b, c, d, y, transaction_cost)
        else:  # 50%概率观察错误，使用错误的Y值
            wrong_y = np.random.uniform(0.2, 0.8)
            optimal_x, _ = find_optimal_strategy(a, b, c, d, wrong_y, transaction_cost)
            gross_return = calculate_expected_return(a, b, c, d, optimal_x, y)
            net_return = gross_return - transaction_cost
        strategy3_returns.append(net_return)
    
    # 结果统计
    print("100个交易周期的累计收益对比：")
    print(f"策略1（不观察Y）：{sum(strategy1_returns)*100:.2f}%")
    print(f"策略2（完美观察Y）：{sum(strategy2_returns)*100:.2f}%")
    print(f"策略3（部分观察Y）：{sum(strategy3_returns)*100:.2f}%")
    print()
    
    print("平均单次收益：")
    print(f"策略1：{np.mean(strategy1_returns)*100:.3f}%")
    print(f"策略2：{np.mean(strategy2_returns)*100:.3f}%")
    print(f"策略3：{np.mean(strategy3_returns)*100:.3f}%")
    print()
    
    # 计算观察Y值的价值
    perfect_value = np.mean(strategy2_returns) - np.mean(strategy1_returns)
    partial_value = np.mean(strategy3_returns) - np.mean(strategy1_returns)
    
    print(f"完美观察Y的价值：{perfect_value*100:.3f}%")
    print(f"部分观察Y的价值：{partial_value*100:.3f}%")

def main():
    analyze_y_observation_value()
    analyze_y_prediction_difficulty()
    calculate_y_observation_value()
    analyze_practical_implications()
    simulate_y_observation_scenarios()
    
    print("\n" + "="*80)
    print("🎯 总结：观察Y值对股民的作用")
    print("="*80)
    print()
    print("【核心结论】")
    print("-" * 60)
    print("1. 理论价值：观察Y值确实有助于优化策略")
    print("2. 实际困难：准确观察Y值非常困难且成本高昂")
    print("3. 收益有限：即使完美观察Y值，在不利市场结构下仍难盈利")
    print("4. 风险较高：错误判断Y值可能导致更大损失")
    print()
    print("【建议】")
    print("-" * 60)
    print("• 普通散户：不建议花费精力观察Y值")
    print("• 专业投资者：可以作为辅助分析工具")
    print("• 量化交易者：可以系统化地利用Y值信息")
    print("• 所有投资者：重点关注降低交易成本和长期价值投资")

if __name__ == "__main__":
    main()
