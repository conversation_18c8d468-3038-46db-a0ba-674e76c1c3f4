import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 下载历史数据
ticker = '0001.HK'  # 港交所股票代码
data = yf.download(ticker, start='2023-01-01', end='2023-10-01')

# 数据预处理
data['SMA5'] = data['Close'].rolling(window=5).mean()
data['SMA20'] = data['Close'].rolling(window=20).mean()

# 生成信号：当短期均值线（SMA5）从下向上穿过长期均值线（SMA20）时买入
data['Signal'] = np.where(data['SMA5'] > data['SMA20'], 1, 0)
data['Position'] = data['Signal'].diff()

# 回测策略
initial_capital = 125000
positions = pd.DataFrame(index=data.index).fillna(0.0)
portfolio = pd.DataFrame(index=data.index).fillna(0.0)

# 开始回测
for i in range(len(data)):
    if data['Position'][i] == 1:
        shares = initial_capital / data['Close'][i]
        positions[ticker][i:] = shares

portfolio[ticker] = positions[ticker] * data['Close']
portfolio['Total'] = portfolio.sum(axis=1)

# 绘制回测结果
fig, ax1 = plt.subplots(figsize=(15, 8))

color = 'tab:blue'
ax1.set_xlabel('Date')
ax1.set_ylabel('Price', color=color)
ax1.plot(data.index, data['Close'], label='Close Price', color=color)
ax1.tick_params(axis='y', labelcolor=color)

ax2 = ax1.twinx()
color = 'tab:green'
ax2.set_ylabel('Portfolio Value (HKD)', color=color)
ax2.plot(portfolio.index, portfolio['Total'], label='Portfolio Value', color=color)
ax2.tick_params(axis='y', labelcolor=color)

fig.tight_layout()  # 调整子图参数，使之填充整个图像区域
plt.title('Quantitative Trading Backtest - 0001.HK')
plt.show()

# 输出最终结果
final_value = portfolio['Total'].iloc[-1]
return_percentage = ((final_value - initial_capital) / initial_capital) * 100

print(f"Initial Capital: HKD {initial_capital:,}")
print(f"Final Portfolio Value: HKD {final_value:.2f}")
print(f"Total Return: {return_percentage:.2f}%")
