-- 增强版的线性回归存储过程，添加Y值估计功能
DELIMITER $$

USE `finance`$$

DROP PROCEDURE IF EXISTS `sp_averagelineV4_enhanced`$$

CREATE DEFINER=`root`@`%` PROCEDURE `sp_averagelineV4_enhanced`(IN tablename VARCHAR(64))
BEGIN
  DECLARE Xavg DECIMAL(20, 6) DEFAULT 0;
  DECLARE Yavg DECIMAL(20, 6) DEFAULT 0;
  DECLARE sum_xy DECIMAL(20, 6) DEFAULT 0;
  DECLARE sum_xx DECIMAL(20, 6) DEFAULT 0;
  DECLARE ss_res DECIMAL(20, 6) DEFAULT 0;
  DECLARE ss_tot DECIMAL(20, 6) DEFAULT 0;
  DECLARE b DECIMAL(20, 6) DEFAULT 0;
  DECLARE m DECIMAL(20, 6) DEFAULT 0;
  DECLARE r_squared DECIMAL(20, 6) DEFAULT 0;
  DECLARE estimated_y DECIMAL(20, 6) DEFAULT 0;
  DECLARE trend_strength VARCHAR(20) DEFAULT '';
  DECLARE trading_signal VARCHAR(20) DEFAULT '';
  DECLARE total_rows INT DEFAULT 0;
  DECLARE col_exists INT DEFAULT 0;
  DECLARE stmt_sql TEXT;

  -- 检查并添加 i 列
  SET @col_exists = 0;
  SET stmt_sql = CONCAT(
    'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
    'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=\'', tablename, '\' AND COLUMN_NAME=\'i\''
  );
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET col_exists = @col_exists;
  
  IF col_exists = 0 THEN
    SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `i` INT');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET @rownum := 0;
    SET stmt_sql = CONCAT('UPDATE `', tablename, '` SET i = (@rownum := @rownum + 1)');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  END IF;

  -- 获取总行数
  SET @total_rows = 0;
  SET stmt_sql = CONCAT('SELECT COUNT(*) INTO @total_rows FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET total_rows = @total_rows;

  -- 计算平均值
  SET Xavg = (total_rows + 1) / 2;  -- 修正：应该是(n+1)/2
  
  SET @Yavg = 0;
  SET stmt_sql = CONCAT('SELECT AVG(收市) INTO @Yavg FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET Yavg = @Yavg;

  -- 计算回归系数
  SET @sum_xy = 0;
  SET stmt_sql = CONCAT('SELECT SUM((i - ', Xavg, ') * (收市 - ', Yavg, ')) INTO @sum_xy FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET sum_xy = @sum_xy;

  SET @sum_xx = 0;
  SET stmt_sql = CONCAT('SELECT SUM(POWER(i - ', Xavg, ', 2)) INTO @sum_xx FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET sum_xx = @sum_xx;

  -- 计算斜率和截距
  SET b = sum_xy / sum_xx;
  SET m = Yavg - b * Xavg;

  -- 计算R²
  SET @ss_res = 0;
  SET stmt_sql = CONCAT('SELECT SUM(POWER(收市 - (', m, ' + ', b, ' * i), 2)) INTO @ss_res FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET ss_res = @ss_res;

  SET @ss_tot = 0;
  SET stmt_sql = CONCAT('SELECT SUM(POWER(收市 - ', Yavg, ', 2)) INTO @ss_tot FROM `', tablename, '`');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET ss_tot = @ss_tot;

  IF ss_tot > 0 THEN
    SET r_squared = 1 - (ss_res / ss_tot);
  ELSE
    SET r_squared = 0;
  END IF;

  -- 基于斜率估计Y值和趋势强度
  CASE 
    WHEN b > 1.0 THEN 
      SET estimated_y = 0.7;
      SET trend_strength = '强烈上涨';
    WHEN b > 0.5 THEN 
      SET estimated_y = 0.65;
      SET trend_strength = '明显上涨';
    WHEN b > 0.3 THEN 
      SET estimated_y = 0.6;
      SET trend_strength = '温和上涨';
    WHEN b > -0.3 THEN 
      SET estimated_y = 0.5;
      SET trend_strength = '横盘整理';
    WHEN b > -0.5 THEN 
      SET estimated_y = 0.4;
      SET trend_strength = '温和下跌';
    WHEN b > -1.0 THEN 
      SET estimated_y = 0.35;
      SET trend_strength = '明显下跌';
    ELSE 
      SET estimated_y = 0.3;
      SET trend_strength = '强烈下跌';
  END CASE;

  -- 生成交易信号
  IF estimated_y > 0.4 OR estimated_y < 0.33 THEN
    IF r_squared > 0.5 THEN
      SET trading_signal = '可以交易';
    ELSE
      SET trading_signal = '谨慎交易';
    END IF;
  ELSE
    SET trading_signal = '避免交易';
  END IF;

  -- 检查并添加"中值"列
  SET @col_exists = 0;
  SET stmt_sql = CONCAT(
    'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
    'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=\'', tablename, '\' AND COLUMN_NAME=\'中值\''
  );
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET col_exists = @col_exists;
  
  IF col_exists = 0 THEN
    SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `中值` DECIMAL(20, 6)');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  END IF;

  -- 检查并添加"偏离度"列
  SET @col_exists = 0;
  SET stmt_sql = CONCAT(
    'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
    'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=\'', tablename, '\' AND COLUMN_NAME=\'偏离度\''
  );
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  SET col_exists = @col_exists;
  
  IF col_exists = 0 THEN
    SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `偏离度` DECIMAL(20, 6)');
    PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
  END IF;

  -- 更新计算列
  SET stmt_sql = CONCAT('UPDATE `', tablename, '` SET `中值` = ', m, ' + ', b, ' * i');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

  SET stmt_sql = CONCAT('UPDATE `', tablename, '` SET `偏离度` = (收市 - 中值) / 中值 * 100');
  PREPARE stmt FROM stmt_sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

  -- 返回增强的分析结果
  SELECT 
    m AS 截距,
    b AS 斜率,
    r_squared AS R平方,
    estimated_y AS 估计Y值,
    trend_strength AS 趋势强度,
    trading_signal AS 交易信号,
    CASE 
      WHEN r_squared > 0.7 THEN '很好'
      WHEN r_squared > 0.5 THEN '良好'
      WHEN r_squared > 0.3 THEN '一般'
      ELSE '较差'
    END AS 拟合质量,
    sum_xy AS SUM_xy,
    sum_xx AS SUM_xx,
    Xavg AS X平均,
    Yavg AS Y平均,
    total_rows AS 总行数;

END$$

DELIMITER ;

-- 使用示例：
-- CALL sp_averagelineV4_enhanced('your_stock_table');

-- 查询结果示例：
-- SELECT 截距, 斜率, R平方, 估计Y值, 趋势强度, 交易信号, 拟合质量 
-- FROM (CALL sp_averagelineV4_enhanced('your_stock_table')) AS result;
