#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票博弈论分析程序
分析股民与控股商之间的博弈，寻找使股民期望收益E最大的参数组合

期望值公式：E = (a-b-c+d)xy + (b-d)x + (c-d)y + d

其中：
- x: 股民买升的概率 (0 < x < 1)
- y: 控股商托高股价的概率 (固定为 0.495858373)
- a: 股民买升，控股商托价时的收益 (正整数 1-50)
- b: 股民买跌，控股商托价时的收益 (负整数 -50到-1)
- c: 股民买升，控股商压价时的收益 (负整数 -50到-1)
- d: 股民买跌，控股商压价时的收益 (正整数 1-50)
"""

import numpy as np
import pandas as pd
from itertools import product
import matplotlib.pyplot as plt

def calculate_expected_value(a, b, c, d, x, y):
    """
    计算期望值E
    
    参数:
    a, b, c, d: 四种情况下的收益
    x: 股民买升的概率
    y: 控股商托高股价的概率
    
    返回:
    期望值E
    """
    E = (a - b - c + d) * x * y + (b - d) * x + (c - d) * y + d
    return E

def find_optimal_parameters():
    """
    寻找使期望值E最大的参数组合
    """
    # 固定参数
    y = 0.495858373
    x_values = np.arange(0.1, 1.0, 0.1)  # x从0.1到0.9，步长0.1
    
    # 参数范围
    a_values = range(1, 51)      # a: 正整数 1-50
    d_values = range(1, 51)      # d: 正整数 1-50
    b_values = range(-50, 0)     # b: 负整数 -50到-1
    c_values = range(-50, 0)     # c: 负整数 -50到-1
    
    max_E = float('-inf')
    best_params = None
    results = []
    
    print("开始搜索最优参数组合...")
    print(f"固定参数 y = {y}")
    print(f"搜索范围：a∈[1,50], b∈[-50,-1], c∈[-50,-1], d∈[1,50], x∈[0.1,0.9]")
    print("-" * 60)
    
    # 遍历所有参数组合
    total_combinations = len(a_values) * len(b_values) * len(c_values) * len(d_values) * len(x_values)
    current_count = 0
    
    for a, b, c, d in product(a_values, b_values, c_values, d_values):
        for x in x_values:
            current_count += 1
            if current_count % 100000 == 0:
                print(f"进度: {current_count}/{total_combinations} ({current_count/total_combinations*100:.1f}%)")
            
            E = calculate_expected_value(a, b, c, d, x, y)
            
            # 记录结果
            results.append({
                'a': a, 'b': b, 'c': c, 'd': d, 'x': round(x, 1), 'y': y, 'E': E
            })
            
            # 更新最优值
            if E > max_E:
                max_E = E
                best_params = {'a': a, 'b': b, 'c': c, 'd': d, 'x': round(x, 1), 'y': y, 'E': E}
    
    return best_params, results

def analyze_results(best_params, results):
    """
    分析结果
    """
    print("\n" + "="*60)
    print("最优参数组合:")
    print("="*60)
    print(f"a (股民买升，控股商托价收益): {best_params['a']}")
    print(f"b (股民买跌，控股商托价收益): {best_params['b']}")
    print(f"c (股民买升，控股商压价收益): {best_params['c']}")
    print(f"d (股民买跌，控股商压价收益): {best_params['d']}")
    print(f"x (股民买升概率): {best_params['x']}")
    print(f"y (控股商托价概率): {best_params['y']}")
    print(f"最大期望收益 E: {best_params['E']:.6f}")
    
    # 转换为DataFrame进行分析
    df = pd.DataFrame(results)
    
    # 显示前10个最优结果
    print("\n" + "="*60)
    print("前10个最优结果:")
    print("="*60)
    top_10 = df.nlargest(10, 'E')
    print(top_10.to_string(index=False))
    
    # 统计分析
    print("\n" + "="*60)
    print("统计分析:")
    print("="*60)
    print(f"期望收益E的统计信息:")
    print(f"最大值: {df['E'].max():.6f}")
    print(f"最小值: {df['E'].min():.6f}")
    print(f"平均值: {df['E'].mean():.6f}")
    print(f"标准差: {df['E'].std():.6f}")
    
    return df

def plot_results(df, best_params):
    """
    绘制结果图表
    """
    plt.figure(figsize=(15, 10))
    
    # 子图1: E值分布直方图
    plt.subplot(2, 3, 1)
    plt.hist(df['E'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(best_params['E'], color='red', linestyle='--', linewidth=2, label=f'最大值: {best_params["E"]:.3f}')
    plt.xlabel('期望收益 E')
    plt.ylabel('频次')
    plt.title('期望收益E的分布')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: x对E的影响（固定最优a,b,c,d）
    plt.subplot(2, 3, 2)
    optimal_subset = df[(df['a'] == best_params['a']) & 
                       (df['b'] == best_params['b']) & 
                       (df['c'] == best_params['c']) & 
                       (df['d'] == best_params['d'])]
    plt.plot(optimal_subset['x'], optimal_subset['E'], 'bo-', linewidth=2, markersize=6)
    plt.xlabel('x (股民买升概率)')
    plt.ylabel('期望收益 E')
    plt.title('最优参数下x对E的影响')
    plt.grid(True, alpha=0.3)
    
    # 子图3: a值对最大E的影响
    plt.subplot(2, 3, 3)
    a_effect = df.groupby('a')['E'].max()
    plt.plot(a_effect.index, a_effect.values, 'go-', linewidth=2, markersize=4)
    plt.xlabel('a值')
    plt.ylabel('最大期望收益 E')
    plt.title('a值对最大E的影响')
    plt.grid(True, alpha=0.3)
    
    # 子图4: b值对最大E的影响
    plt.subplot(2, 3, 4)
    b_effect = df.groupby('b')['E'].max()
    plt.plot(b_effect.index, b_effect.values, 'ro-', linewidth=2, markersize=4)
    plt.xlabel('b值')
    plt.ylabel('最大期望收益 E')
    plt.title('b值对最大E的影响')
    plt.grid(True, alpha=0.3)
    
    # 子图5: c值对最大E的影响
    plt.subplot(2, 3, 5)
    c_effect = df.groupby('c')['E'].max()
    plt.plot(c_effect.index, c_effect.values, 'mo-', linewidth=2, markersize=4)
    plt.xlabel('c值')
    plt.ylabel('最大期望收益 E')
    plt.title('c值对最大E的影响')
    plt.grid(True, alpha=0.3)
    
    # 子图6: d值对最大E的影响
    plt.subplot(2, 3, 6)
    d_effect = df.groupby('d')['E'].max()
    plt.plot(d_effect.index, d_effect.values, 'co-', linewidth=2, markersize=4)
    plt.xlabel('d值')
    plt.ylabel('最大期望收益 E')
    plt.title('d值对最大E的影响')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('stock_game_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    print("股票博弈论分析程序")
    print("="*60)
    
    # 寻找最优参数
    best_params, results = find_optimal_parameters()
    
    # 分析结果
    df = analyze_results(best_params, results)
    
    # 保存结果到CSV文件
    df.to_csv('stock_game_results.csv', index=False, encoding='utf-8-sig')
    print(f"\n结果已保存到 stock_game_results.csv 文件")
    
    # 绘制图表
    try:
        plot_results(df, best_params)
        print("图表已保存为 stock_game_analysis.png")
    except Exception as e:
        print(f"绘图时出现错误: {e}")
    
    # 验证最优解
    print("\n" + "="*60)
    print("验证最优解:")
    print("="*60)
    a, b, c, d, x, y = best_params['a'], best_params['b'], best_params['c'], best_params['d'], best_params['x'], best_params['y']
    
    print(f"使用公式验证: E = (a-b-c+d)xy + (b-d)x + (c-d)y + d")
    print(f"E = ({a}-({b})-({c})+{d})*{x}*{y} + ({b}-{d})*{x} + ({c}-{d})*{y} + {d}")
    
    term1 = (a - b - c + d) * x * y
    term2 = (b - d) * x
    term3 = (c - d) * y
    term4 = d
    
    print(f"第一项: ({a - b - c + d})*{x}*{y} = {term1:.6f}")
    print(f"第二项: ({b - d})*{x} = {term2:.6f}")
    print(f"第三项: ({c - d})*{y} = {term3:.6f}")
    print(f"第四项: {d} = {term4:.6f}")
    print(f"总和: {term1 + term2 + term3 + term4:.6f}")

if __name__ == "__main__":
    main()
